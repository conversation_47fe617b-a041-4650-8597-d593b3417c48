"""
AI文本信息提炼API测试客户端
演示如何使用API的各种功能
"""

import requests
import json
import time
from pathlib import Path

class AITextAnalysisClient:
    """AI文本分析API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def health_check(self):
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def get_models_status(self):
        """获取模型状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/models/status")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_text(self, text: str, analysis_type: str = "summary", 
                    model_provider: str = None, custom_prompt: str = None):
        """分析单个文本"""
        url = f"{self.base_url}/api/v1/analyze/text"
        data = {
            "text": text,
            "analysis_type": analysis_type
        }
        
        if model_provider:
            data["model_provider"] = model_provider
        if custom_prompt:
            data["custom_prompt"] = custom_prompt
        
        try:
            response = self.session.post(url, json=data)
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_batch(self, texts: list, analysis_type: str = "summary",
                     model_provider: str = None, custom_prompt: str = None):
        """批量分析文本"""
        url = f"{self.base_url}/api/v1/analyze/batch"
        data = {
            "texts": texts,
            "analysis_type": analysis_type
        }
        
        if model_provider:
            data["model_provider"] = model_provider
        if custom_prompt:
            data["custom_prompt"] = custom_prompt
        
        try:
            response = self.session.post(url, json=data)
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_file(self, file_path: str, analysis_type: str = "summary",
                    model_provider: str = None, custom_prompt: str = None):
        """分析文件"""
        url = f"{self.base_url}/api/v1/analyze/file"
        
        data = {
            "analysis_type": analysis_type
        }
        
        if model_provider:
            data["model_provider"] = model_provider
        if custom_prompt:
            data["custom_prompt"] = custom_prompt
        
        try:
            with open(file_path, 'rb') as f:
                files = {"file": f}
                response = self.session.post(url, files=files, data=data)
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_custom(self, text: str, prompt: str, model_provider: str = None):
        """自定义提示词分析"""
        url = f"{self.base_url}/api/v1/analyze/custom"
        data = {
            "text": text,
            "prompt": prompt
        }
        
        if model_provider:
            data["model_provider"] = model_provider
        
        try:
            response = self.session.post(url, json=data)
            return response.json()
        except Exception as e:
            return {"error": str(e)}

def demo_single_text_analysis(client):
    """演示单文本分析"""
    print("=== 单文本分析演示 ===")
    
    sample_text = """
    标题: 盘点一周AI大事(9月7日)｜AI预设MBTI OpenAI自研AI芯片明年量产
    内容: 一分钟看完一周AI大事opai自研AI芯片明年量产，目标是减少对英伟达的依赖，降低AI推理成本。
    Anthropic估值暴涨三倍，成为全球第四独角兽。阿里发布最大模型参数规模1万亿，跑分超越deep sick。
    字节推出最强图像模型，图像编辑能力反超nano banana。
    """
    
    # 测试不同的分析类型
    analysis_types = ["summary", "extract", "keywords", "sentiment", "classify"]
    
    for analysis_type in analysis_types:
        print(f"\n--- {analysis_type} 分析 ---")
        result = client.analyze_text(sample_text, analysis_type)
        
        if result.get("success"):
            print(f"分析结果: {result['data']['result'][:200]}...")
            print(f"处理时间: {result['data']['processing_time']:.2f}秒")
        else:
            print(f"分析失败: {result.get('error', '未知错误')}")
        
        time.sleep(1)  # 避免请求过于频繁

def demo_batch_analysis(client):
    """演示批量分析"""
    print("\n=== 批量文本分析演示 ===")
    
    sample_texts = [
        "OpenAI自研AI芯片明年量产，目标是减少对英伟达的依赖。",
        "Anthropic估值暴涨三倍，成为全球第四独角兽。",
        "阿里发布最大模型参数规模1万亿，跑分超越deep sick。"
    ]
    
    result = client.analyze_batch(sample_texts, "summary")
    
    if result.get("success"):
        print(f"批量分析完成，成功: {result['successful_count']}, 失败: {result['failed_count']}")
        for i, item in enumerate(result['results']):
            if item['success']:
                print(f"文本{i+1}: {item['result'][:100]}...")
            else:
                print(f"文本{i+1}分析失败: {item.get('error', '未知错误')}")
    else:
        print(f"批量分析失败: {result.get('error', '未知错误')}")

def demo_file_analysis(client):
    """演示文件分析"""
    print("\n=== 文件分析演示 ===")
    
    # 检查文件是否存在
    file_path = "extracted_texts_20250911_122444.txt"
    if Path(file_path).exists():
        result = client.analyze_file(file_path, "summary")
        
        if result.get("success"):
            print(f"文件分析完成，总视频数: {result['total_videos']}")
            print(f"成功处理: {result['successful_count']}, 失败: {result['failed_count']}")
            print(f"总处理时间: {result['total_processing_time']:.2f}秒")
            
            # 显示前3个结果
            for i, item in enumerate(result['results'][:3]):
                if item['success']:
                    print(f"\n视频{i+1} ({item['video_id']}):")
                    print(f"标题: {item['title']}")
                    print(f"分析结果: {item['analysis_result'][:150]}...")
        else:
            print(f"文件分析失败: {result.get('error', '未知错误')}")
    else:
        print(f"文件 {file_path} 不存在，跳过文件分析演示")

def demo_custom_analysis(client):
    """演示自定义分析"""
    print("\n=== 自定义提示词分析演示 ===")
    
    sample_text = """
    字节推出最强图像模型Seeddream 4.0，图像编辑能力反超nano banana，目前正在灰度测试。
    鹅厂开源最强世界模型，融合视频生成与三弟重建，支持自定义视角和记忆。
    """
    
    custom_prompt = """
    请从商业角度分析这段文本，重点关注：
    1. 涉及的公司和产品
    2. 技术创新点
    3. 市场竞争态势
    4. 潜在的商业价值
    """
    
    result = client.analyze_custom(sample_text, custom_prompt)
    
    if result.get("success"):
        print(f"自定义分析结果: {result['data']['result']}")
        print(f"处理时间: {result['data']['processing_time']:.2f}秒")
    else:
        print(f"自定义分析失败: {result.get('error', '未知错误')}")

def main():
    """主函数"""
    print("🤖 AI文本信息提炼API测试客户端")
    print("=" * 50)
    
    # 创建客户端
    client = AITextAnalysisClient()
    
    # 健康检查
    print("检查API服务状态...")
    health = client.health_check()
    if health.get("status") == "healthy":
        print("✅ API服务运行正常")
    else:
        print("❌ API服务异常:", health.get("error", "未知错误"))
        return
    
    # 检查模型状态
    print("\n检查AI模型状态...")
    models_status = client.get_models_status()
    if models_status.get("success"):
        data = models_status["data"]
        print(f"OpenAI可用: {data['openai']['available']}")
        print(f"Anthropic可用: {data['anthropic']['available']}")
        print(f"默认提供商: {data['default_provider']}")
    else:
        print("❌ 获取模型状态失败:", models_status.get("error", "未知错误"))
        return
    
    # 运行演示
    try:
        demo_single_text_analysis(client)
        demo_batch_analysis(client)
        demo_file_analysis(client)
        demo_custom_analysis(client)
        
        print("\n🎉 所有演示完成！")
        print("💡 你可以访问 http://localhost:8000/docs 查看完整的API文档")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
