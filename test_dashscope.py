#!/usr/bin/env python3
"""
测试通义千问API直接调用
"""

import asyncio
import os
from app.services.ai_service import AIService
from app.models.requests import AnalysisType

async def test_dashscope_direct():
    """直接测试通义千问API"""
    print("🧪 测试通义千问API直接调用")
    print("=" * 40)
    
    try:
        # 创建AI服务实例
        ai_service = AIService()
        
        # 检查通义千问是否可用
        from app.models.requests import ModelProvider
        is_available = ai_service.is_available(ModelProvider.DASHSCOPE)
        print(f"通义千问服务可用: {is_available}")
        
        if not is_available:
            print("❌ 通义千问服务不可用，请检查API密钥配置")
            return
        
        # 测试文本分析
        test_text = """
        标题: 盘点一周AI大事(9月7日)｜AI预设MBTI OpenAI自研AI芯片明年量产
        内容: 一分钟看完一周AI大事opai自研AI芯片明年量产，目标是减少对英伟达的依赖，降低AI推理成本。
        Anthropic估值暴涨三倍，成为全球第四独角兽。阿里发布最大模型参数规模1万亿，跑分超越deep sick。
        """
        
        print(f"\n📝 测试文本: {test_text[:100]}...")
        
        # 测试摘要生成
        print("\n🔍 测试摘要生成...")
        result, token_usage, model_used = await ai_service.analyze_text(
            text=test_text,
            analysis_type=AnalysisType.SUMMARY,
            model_provider=ModelProvider.DASHSCOPE
        )
        
        print(f"✅ 分析成功!")
        print(f"   使用模型: {model_used}")
        print(f"   处理时间: {token_usage.get('processing_time', 0):.2f}秒")
        print(f"   Token使用: {token_usage.get('total_tokens', 0)}")
        print(f"   分析结果: {result[:200]}...")
        
        # 测试关键信息提取
        print("\n🔍 测试关键信息提取...")
        result2, token_usage2, model_used2 = await ai_service.analyze_text(
            text=test_text,
            analysis_type=AnalysisType.EXTRACT,
            model_provider=ModelProvider.DASHSCOPE
        )
        
        print(f"✅ 提取成功!")
        print(f"   处理时间: {token_usage2.get('processing_time', 0):.2f}秒")
        print(f"   提取结果: {result2[:200]}...")
        
        print("\n🎉 通义千问API测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    asyncio.run(test_dashscope_direct())

if __name__ == "__main__":
    main()
