#!/usr/bin/env python3
"""
AI文本信息提炼API启动脚本
提供便捷的启动方式和配置检查
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import openai
        import anthropic
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  未找到 .env 文件")
        print("请复制 .env.example 到 .env 并配置API密钥")
        
        # 询问是否自动创建
        response = input("是否自动创建 .env 文件? (y/n): ").lower()
        if response == 'y':
            try:
                import shutil
                shutil.copy('.env.example', '.env')
                print("✅ 已创建 .env 文件，请编辑并配置API密钥")
                return False  # 需要用户手动配置
            except Exception as e:
                print(f"❌ 创建 .env 文件失败: {e}")
                return False
        return False
    
    # 检查API密钥配置
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    has_openai = 'OPENAI_API_KEY=' in content and 'your_openai_api_key_here' not in content
    has_anthropic = 'ANTHROPIC_API_KEY=' in content and 'your_anthropic_api_key_here' not in content
    
    if not has_openai and not has_anthropic:
        print("⚠️  请在 .env 文件中配置至少一个AI服务的API密钥")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def create_directories():
    """创建必要的目录"""
    directories = ['logs', 'uploads']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✅ 目录结构检查完成")

def start_server(host="0.0.0.0", port=8000, reload=True):
    """启动API服务器"""
    print(f"🚀 启动AI文本信息提炼API服务...")
    print(f"📍 地址: http://{host}:{port}")
    print(f"📚 API文档: http://{host}:{port}/docs")
    print(f"📖 ReDoc文档: http://{host}:{port}/redoc")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🤖 AI文本信息提炼API启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 检查环境配置
    if not check_env_file():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='AI文本信息提炼API启动器')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8000, help='监听端口 (默认: 8000)')
    parser.add_argument('--no-reload', action='store_true', help='禁用自动重载')
    
    args = parser.parse_args()
    
    # 启动服务器
    start_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload
    )

if __name__ == "__main__":
    main()
