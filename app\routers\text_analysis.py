"""
文本分析API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Depends
from fastapi.responses import JSONResponse
import time
import asyncio
from typing import List
import os
from pathlib import Path

from app.models.requests import (
    TextAnalysisRequest, 
    BatchAnalysisRequest, 
    FileAnalysisRequest,
    CustomAnalysisRequest,
    AnalysisType
)
from app.models.responses import (
    TextAnalysisResponse,
    BatchAnalysisResponse,
    FileAnalysisResponse,
    AnalysisResult,
    BatchAnalysisResult,
    VideoAnalysisResult,
    ErrorResponse
)
from app.services.ai_service import ai_service
from app.utils.text_parser import TextParser
from app.config import settings

router = APIRouter()
text_parser = TextParser()

@router.post("/analyze/text", response_model=TextAnalysisResponse)
async def analyze_single_text(request: TextAnalysisRequest):
    """
    分析单个文本
    """
    try:
        start_time = time.time()
        
        # 验证文本长度
        if len(request.text) > settings.MAX_TEXT_LENGTH:
            raise HTTPException(
                status_code=400,
                detail=f"文本长度超过限制({settings.MAX_TEXT_LENGTH}字符)"
            )
        
        # 调用AI服务进行分析
        result, token_usage, model_used = await ai_service.analyze_text(
            text=request.text,
            analysis_type=request.analysis_type,
            model_provider=request.model_provider,
            custom_prompt=request.custom_prompt
        )
        
        processing_time = time.time() - start_time
        
        analysis_result = AnalysisResult(
            result=result,
            analysis_type=request.analysis_type.value,
            model_used=model_used,
            processing_time=processing_time,
            token_usage=token_usage
        )
        
        return TextAnalysisResponse(
            success=True,
            data=analysis_result
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/analyze/batch", response_model=BatchAnalysisResponse)
async def analyze_batch_texts(request: BatchAnalysisRequest):
    """
    批量分析文本
    """
    try:
        start_time = time.time()
        
        # 验证批量大小
        if len(request.texts) > settings.MAX_BATCH_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"批量大小超过限制({settings.MAX_BATCH_SIZE})"
            )
        
        results = []
        successful_count = 0
        failed_count = 0
        model_used = ""
        
        # 并发处理多个文本
        async def process_single_text(index: int, text: str):
            try:
                result, token_usage, model = await ai_service.analyze_text(
                    text=text,
                    analysis_type=request.analysis_type,
                    model_provider=request.model_provider,
                    custom_prompt=request.custom_prompt
                )
                return BatchAnalysisResult(
                    index=index,
                    result=result,
                    success=True
                )
            except Exception as e:
                return BatchAnalysisResult(
                    index=index,
                    result="",
                    success=False,
                    error=str(e)
                )
        
        # 创建并发任务
        tasks = [
            process_single_text(i, text) 
            for i, text in enumerate(request.texts)
        ]
        
        # 执行并发任务
        results = await asyncio.gather(*tasks)
        
        # 统计结果
        for result in results:
            if result.success:
                successful_count += 1
            else:
                failed_count += 1
        
        # 获取模型信息（从第一个成功的结果）
        if successful_count > 0:
            _, _, model_used = await ai_service.analyze_text(
                text=request.texts[0][:100],  # 用短文本获取模型信息
                analysis_type=request.analysis_type,
                model_provider=request.model_provider
            )
        
        total_processing_time = time.time() - start_time
        
        return BatchAnalysisResponse(
            success=successful_count > 0,
            results=results,
            analysis_type=request.analysis_type.value,
            model_used=model_used,
            total_processing_time=total_processing_time,
            successful_count=successful_count,
            failed_count=failed_count
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/analyze/file", response_model=FileAnalysisResponse)
async def analyze_extracted_texts_file(
    file: UploadFile = File(...),
    analysis_type: AnalysisType = AnalysisType.SUMMARY,
    model_provider: str = None,
    custom_prompt: str = None,
    extract_by_video: bool = False
):
    """
    分析上传的extracted_texts文件
    """
    try:
        start_time = time.time()
        
        # 验证文件类型
        if not file.filename.endswith('.txt'):
            raise HTTPException(
                status_code=400,
                detail="只支持.txt文件"
            )
        
        # 验证文件大小
        content = await file.read()
        if len(content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"文件大小超过限制({settings.MAX_FILE_SIZE}字节)"
            )
        
        # 解析文件内容
        try:
            content_str = content.decode('utf-8')
            extracted_items = text_parser.parse_extracted_texts_content(content_str)
        except UnicodeDecodeError:
            raise HTTPException(
                status_code=400,
                detail="文件编码错误，请确保文件为UTF-8编码"
            )
        
        if not extracted_items:
            raise HTTPException(
                status_code=400,
                detail="文件中没有找到有效的文本内容"
            )
        
        results = []
        successful_count = 0
        failed_count = 0
        model_used = ""
        
        # 处理每个视频的文本
        async def process_video_text(item):
            try:
                # 组合标题和内容
                full_text = f"标题: {item.title}\n内容: {item.content}"
                
                result, token_usage, model = await ai_service.analyze_text(
                    text=full_text,
                    analysis_type=analysis_type,
                    model_provider=model_provider,
                    custom_prompt=custom_prompt
                )
                
                return VideoAnalysisResult(
                    video_id=item.video_id,
                    title=item.title,
                    analysis_result=result,
                    success=True
                )
            except Exception as e:
                return VideoAnalysisResult(
                    video_id=item.video_id,
                    title=item.title,
                    analysis_result="",
                    success=False,
                    error=str(e)
                )
        
        # 创建并发任务（限制并发数量以避免API限制）
        semaphore = asyncio.Semaphore(3)  # 最多3个并发请求
        
        async def process_with_semaphore(item):
            async with semaphore:
                return await process_video_text(item)
        
        tasks = [process_with_semaphore(item) for item in extracted_items]
        results = await asyncio.gather(*tasks)
        
        # 统计结果
        for result in results:
            if result.success:
                successful_count += 1
            else:
                failed_count += 1
        
        # 获取模型信息
        if successful_count > 0:
            _, _, model_used = await ai_service.analyze_text(
                text="test",
                analysis_type=analysis_type,
                model_provider=model_provider
            )
        
        total_processing_time = time.time() - start_time
        
        return FileAnalysisResponse(
            success=successful_count > 0,
            results=results,
            analysis_type=analysis_type.value,
            model_used=model_used,
            total_videos=len(extracted_items),
            successful_count=successful_count,
            failed_count=failed_count,
            total_processing_time=total_processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/analyze/custom", response_model=TextAnalysisResponse)
async def analyze_with_custom_prompt(request: CustomAnalysisRequest):
    """
    使用自定义提示词分析文本
    """
    try:
        start_time = time.time()
        
        # 验证文本长度
        if len(request.text) > settings.MAX_TEXT_LENGTH:
            raise HTTPException(
                status_code=400,
                detail=f"文本长度超过限制({settings.MAX_TEXT_LENGTH}字符)"
            )
        
        # 使用自定义提示词
        custom_prompt = f"{request.prompt}\n\n文本内容：\n{request.text}"
        
        # 调用AI服务
        result, token_usage, model_used = await ai_service.analyze_text(
            text=request.text,
            analysis_type=AnalysisType.SUMMARY,  # 使用默认类型
            model_provider=request.model_provider,
            custom_prompt=custom_prompt
        )
        
        processing_time = time.time() - start_time
        
        analysis_result = AnalysisResult(
            result=result,
            analysis_type="custom",
            model_used=model_used,
            processing_time=processing_time,
            token_usage=token_usage
        )
        
        return TextAnalysisResponse(
            success=True,
            data=analysis_result
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/models/status")
async def get_models_status():
    """
    获取AI模型服务状态
    """
    try:
        from app.models.requests import ModelProvider
        
        status = {
            "openai": {
                "available": ai_service.is_available(ModelProvider.OPENAI),
                "model": settings.OPENAI_MODEL if ai_service.is_available(ModelProvider.OPENAI) else None
            },
            "anthropic": {
                "available": ai_service.is_available(ModelProvider.ANTHROPIC),
                "model": settings.ANTHROPIC_MODEL if ai_service.is_available(ModelProvider.ANTHROPIC) else None
            },
            "dashscope": {
                "available": ai_service.is_available(ModelProvider.DASHSCOPE),
                "model": settings.DASHSCOPE_MODEL if ai_service.is_available(ModelProvider.DASHSCOPE) else None
            },
            "default_provider": settings.DEFAULT_MODEL_PROVIDER
        }
        
        return {"success": True, "data": status}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型状态失败: {str(e)}")
