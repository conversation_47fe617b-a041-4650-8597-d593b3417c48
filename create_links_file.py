#!/usr/bin/env python3
"""
创建链接文件工具
"""

def create_links_file():
    print("🔗 创建链接文件工具")
    print("=" * 50)
    print("请粘贴你的所有抖音链接，每行一个")
    print("输入完成后，输入一个空行结束")
    print("-" * 50)
    
    links = []
    while True:
        link = input().strip()
        if not link:
            break
        if 'douyin.com' in link or 'v.douyin.com' in link:
            links.append(link)
            print(f"✅ 添加链接 {len(links)}")
        else:
            print(f"⚠️  不是有效的抖音链接: {link[:50]}...")
    
    if links:
        filename = f"all_links_{len(links)}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            for link in links:
                f.write(link + '\n')
        
        print(f"\n✅ 已保存 {len(links)} 个链接到: {filename}")
        print(f"现在可以运行: python batch_douyin.py")
        print(f"并使用文件名: {filename}")
    else:
        print("❌ 没有有效链接")

if __name__ == "__main__":
    create_links_file()
