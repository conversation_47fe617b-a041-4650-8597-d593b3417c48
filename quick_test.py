#!/usr/bin/env python3
"""
快速测试脚本
"""

def test_imports():
    """测试导入"""
    print("测试导入...")
    try:
        from app.config import settings
        print("✅ 配置模块导入成功")
        
        from app.utils.text_parser import TextParser
        print("✅ 文本解析器导入成功")
        
        from app.services.ai_service import AIService
        print("✅ AI服务模块导入成功")
        
        from app.models.requests import TextAnalysisRequest, AnalysisType
        print("✅ 请求模型导入成功")
        
        from app.models.responses import TextAnalysisResponse
        print("✅ 响应模型导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_text_parser():
    """测试文本解析器"""
    print("\n测试文本解析器...")
    try:
        from app.utils.text_parser import TextParser
        parser = TextParser()
        
        # 测试解析功能
        sample_content = """标题: 测试标题
视频ID: 123456789
文字内容: 这是测试内容
--------------------------------------------------------------------------------

标题: 另一个测试标题
视频ID: 987654321
文字内容: 这是另一个测试内容"""
        
        items = parser.parse_extracted_texts_content(sample_content)
        print(f"✅ 解析成功，共 {len(items)} 个项目")
        
        if items:
            print(f"   第一个项目: {items[0].title}")
        
        return True
    except Exception as e:
        print(f"❌ 文本解析器测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n测试配置...")
    try:
        from app.config import settings
        print(f"✅ 应用名称: {settings.APP_NAME}")
        print(f"✅ 版本: {settings.VERSION}")
        print(f"✅ 端口: {settings.PORT}")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    print("🧪 AI文本信息提炼API - 快速测试")
    print("=" * 40)
    
    success = True
    success &= test_imports()
    success &= test_config()
    success &= test_text_parser()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 所有测试通过！")
        print("\n📋 下一步:")
        print("1. 配置 .env 文件中的API密钥")
        print("2. 运行: python start_api.py")
        print("3. 访问: http://localhost:8000/docs")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
