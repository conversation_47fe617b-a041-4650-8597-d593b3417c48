#!/usr/bin/env python3
"""
直接测试链接加载
"""

def load_links_from_file(filename):
    """从文件加载链接（修复版本）"""
    links = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                print(f"处理第{line_num}行: {repr(line)}")
                
                # 跳过空行和注释行（以#开头）
                if not line or line.startswith('#'):
                    print(f"  跳过（空行或注释）")
                    continue
                
                # 检查是否包含抖音域名
                if 'douyin.com' in line or 'v.douyin.com' in line:
                    links.append(line)
                    print(f"  ✅ 添加到链接列表")
                else:
                    print(f"  ❌ 不是有效的抖音链接")
        
        print(f"\n📁 从文件 {filename} 加载了 {len(links)} 个有效链接")
        return links
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return []
    except Exception as e:
        print(f"❌ 加载出错: {str(e)}")
        return []

def main():
    print("🔍 直接测试链接加载")
    print("=" * 40)
    
    filename = "links.txt"
    links = load_links_from_file(filename)
    
    print(f"\n📊 最终结果: {len(links)} 个链接")
    
    if links:
        print(f"\n📝 链接列表:")
        for i, link in enumerate(links, 1):
            print(f"{i:2d}. {link}")
    
    # 保存结果
    with open('direct_test_result.txt', 'w', encoding='utf-8') as f:
        f.write(f"直接测试结果\n")
        f.write(f"加载的链接数: {len(links)}\n\n")
        for i, link in enumerate(links, 1):
            f.write(f"{i}. {link}\n")

if __name__ == "__main__":
    main()
    print("\n✅ 测试完成，结果已保存到 direct_test_result.txt")
