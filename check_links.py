#!/usr/bin/env python3
"""
检查链接文件工具
验证链接数量和格式
"""

import re
from pathlib import Path

def check_links_file(filename):
    """检查链接文件"""
    print(f"🔍 检查文件: {filename}")
    print("=" * 50)
    
    if not Path(filename).exists():
        print(f"❌ 文件不存在: {filename}")
        return
    
    with open(filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📄 文件总行数: {len(lines)}")
    
    # 统计各种类型的行
    empty_lines = 0
    comment_lines = 0
    valid_links = []
    invalid_lines = []
    
    for line_num, line in enumerate(lines, 1):
        original_line = line
        line = line.strip()
        
        if not line:
            empty_lines += 1
            continue
            
        if line.startswith('#'):
            comment_lines += 1
            continue
        
        # 提取链接（可能包含额外文字）
        douyin_pattern = r'https?://[^\s]*(?:douyin\.com|v\.douyin\.com)[^\s]*'
        matches = re.findall(douyin_pattern, line)
        
        if matches:
            # 取第一个匹配的链接
            clean_link = matches[0].rstrip('/')
            valid_links.append({
                'line_num': line_num,
                'original': original_line.strip(),
                'clean_link': clean_link
            })
        else:
            invalid_lines.append({
                'line_num': line_num,
                'content': line[:100] + ('...' if len(line) > 100 else '')
            })
    
    print(f"📊 统计结果:")
    print(f"   空行: {empty_lines}")
    print(f"   注释行: {comment_lines}")
    print(f"   有效链接: {len(valid_links)}")
    print(f"   无效行: {len(invalid_lines)}")
    
    if invalid_lines:
        print(f"\n⚠️  无效行详情:")
        for item in invalid_lines[:5]:  # 只显示前5个
            print(f"   第{item['line_num']}行: {item['content']}")
        if len(invalid_lines) > 5:
            print(f"   ... 还有 {len(invalid_lines) - 5} 个无效行")
    
    print(f"\n📝 前10个有效链接:")
    for i, item in enumerate(valid_links[:10], 1):
        print(f"   {i}. {item['clean_link']}")
    
    if len(valid_links) > 10:
        print(f"   ... 还有 {len(valid_links) - 10} 个链接")
    
    return valid_links

def save_clean_links(valid_links, output_file="clean_links.txt"):
    """保存清理后的链接"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in valid_links:
            f.write(item['clean_link'] + '\n')
    
    print(f"\n💾 已保存 {len(valid_links)} 个清理后的链接到: {output_file}")
    return output_file

def main():
    """主函数"""
    print("🔗 链接文件检查工具")
    print("=" * 50)
    
    # 检查默认文件
    filename = "links.txt"
    valid_links = check_links_file(filename)
    
    if valid_links:
        # 保存清理后的链接
        clean_file = save_clean_links(valid_links)
        
        print(f"\n✅ 发现 {len(valid_links)} 个有效的抖音链接")
        print(f"📁 原文件: {filename}")
        print(f"🧹 清理后: {clean_file}")
        
        # 提供使用建议
        print(f"\n💡 使用建议:")
        print(f"   1. 使用清理后的文件: {clean_file}")
        print(f"   2. 或者修改 batch_douyin.py 使用改进的链接提取逻辑")
    else:
        print(f"\n❌ 没有找到有效的抖音链接")

if __name__ == "__main__":
    main()
