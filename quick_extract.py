#!/usr/bin/env python3
"""
快速文档内容提炼脚本
自动检测API服务地址并提炼内容
"""

import requests
import json
import time
from pathlib import Path

def find_api_service():
    """查找可用的API服务地址"""
    possible_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://0.0.0.0:8000"
    ]
    
    for url in possible_urls:
        try:
            response = requests.get(f"{url}/health", timeout=3)
            if response.status_code == 200:
                print(f"✅ 找到API服务: {url}")
                return url
        except:
            continue
    
    return None

def extract_summary(api_url, file_path):
    """提取文档摘要"""
    try:
        url = f"{api_url}/api/v1/analyze/file"
        
        with open(file_path, 'rb') as f:
            files = {"file": f}
            data = {
                "analysis_type": "summary",
                "model_provider": "dashscope"
            }
            
            print(f"📤 正在分析文件: {file_path}")
            print("⏳ 请耐心等待，通义千问正在处理...")
            
            response = requests.post(url, files=files, data=data, timeout=300)
            return response.json()
            
    except Exception as e:
        return {"error": str(e)}

def save_summary_results(results, output_file):
    """保存摘要结果"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("🤖 AI文档内容提炼结果 - 摘要版\n")
        f.write("=" * 60 + "\n\n")
        
        if results.get("success"):
            f.write(f"📊 统计信息:\n")
            f.write(f"   总视频数: {results['total_videos']}\n")
            f.write(f"   成功处理: {results['successful_count']}\n")
            f.write(f"   失败数量: {results['failed_count']}\n")
            f.write(f"   处理时间: {results['total_processing_time']:.2f}秒\n")
            f.write(f"   使用模型: {results['model_used']}\n\n")
            
            f.write("📝 视频内容摘要:\n")
            f.write("=" * 40 + "\n\n")
            
            for i, item in enumerate(results['results'], 1):
                if item['success']:
                    f.write(f"{i}. 【{item['video_id']}】\n")
                    f.write(f"   标题: {item['title']}\n")
                    f.write(f"   摘要: {item['analysis_result']}\n")
                    f.write("-" * 50 + "\n\n")
                else:
                    f.write(f"{i}. 【{item['video_id']}】处理失败: {item.get('error', '未知错误')}\n\n")
        else:
            f.write(f"❌ 分析失败: {results.get('error', '未知错误')}\n")

def main():
    """主函数"""
    print("🚀 快速文档内容提炼工具")
    print("=" * 50)
    
    # 1. 查找API服务
    print("🔍 正在查找API服务...")
    api_url = find_api_service()
    
    if not api_url:
        print("❌ 无法连接到API服务")
        print("\n💡 请确保API服务正在运行:")
        print("   python main.py")
        print("   或者: python start_api.py")
        print("\n🔧 如果服务已启动，请检查端口是否被占用")
        return
    
    # 2. 检查文件
    file_path = "extracted_texts_20250911_122444.txt"
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        print("💡 请确保文件在当前目录下")
        return
    
    print(f"📄 找到文件: {file_path}")
    
    # 3. 开始提炼
    print("\n🤖 开始使用通义千问提炼文档核心内容...")
    start_time = time.time()
    
    result = extract_summary(api_url, file_path)
    
    end_time = time.time()
    
    # 4. 处理结果
    if result.get("success"):
        print(f"\n✅ 提炼完成! 总耗时: {end_time - start_time:.2f}秒")
        print(f"📊 处理统计:")
        print(f"   - 总视频数: {result['total_videos']}")
        print(f"   - 成功处理: {result['successful_count']}")
        print(f"   - 失败数量: {result['failed_count']}")
        print(f"   - AI处理时间: {result['total_processing_time']:.2f}秒")
        print(f"   - 使用模型: {result['model_used']}")
        
        # 保存结果
        timestamp = int(time.time())
        output_file = f"文档摘要_{timestamp}.txt"
        save_summary_results(result, output_file)
        
        print(f"\n💾 结果已保存到: {output_file}")
        
        # 显示前3个摘要预览
        print(f"\n📝 前3个视频摘要预览:")
        print("-" * 50)
        
        success_count = 0
        for item in result['results']:
            if item['success'] and success_count < 3:
                success_count += 1
                print(f"\n{success_count}. 【{item['video_id']}】")
                print(f"   标题: {item['title'][:60]}...")
                print(f"   摘要: {item['analysis_result'][:200]}...")
        
        print(f"\n🎉 完成！查看完整结果请打开: {output_file}")
        
    else:
        print(f"\n❌ 提炼失败: {result.get('error', '未知错误')}")
        print("💡 请检查:")
        print("   1. API服务是否正常运行")
        print("   2. 通义千问API密钥是否正确")
        print("   3. 网络连接是否正常")

if __name__ == "__main__":
    main()
