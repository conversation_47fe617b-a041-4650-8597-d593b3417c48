#!/usr/bin/env python3
"""
直接文本提炼工具
模仿 batch_douyin.py 的方式，直接调用AI服务
"""

import os
import sys
import asyncio
import time
from datetime import datetime
from pathlib import Path

# 设置API密钥（使用你提供的密钥）
os.environ['DASHSCOPE_API_KEY'] = 'sk-b00e316a1bfe4bf2996bacf5808f2f96'

# 直接导入AI服务
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.ai_service import AIService
    from app.utils.text_parser import TextParser
    from app.models.requests import AnalysisType, ModelProvider
    print("✅ 成功导入AI服务模块")
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)

class DirectTextExtractor:
    """直接文本提炼器"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.text_parser = TextParser()
        self.results = []
        self.success_count = 0
        self.error_count = 0
    
    def check_service_available(self):
        """检查AI服务是否可用"""
        return self.ai_service.is_available(ModelProvider.DASHSCOPE)
    
    async def analyze_single_video(self, item, index, total, analysis_type="summary"):
        """分析单个视频文本"""
        print(f"\n🔄 处理第 {index}/{total} 个视频...")
        print(f"📺 标题: {item.title[:50]}...")
        
        result = {
            'index': index,
            'video_id': item.video_id,
            'title': item.title,
            'timestamp': datetime.now().isoformat(),
            'analysis_type': analysis_type
        }
        
        try:
            # 组合标题和内容
            full_text = f"标题: {item.title}\n内容: {item.content}"
            
            print(f"🤖 正在使用通义千问分析...")
            
            # 直接调用AI服务
            analysis_result, token_usage, model_used = await self.ai_service.analyze_text(
                text=full_text,
                analysis_type=getattr(AnalysisType, analysis_type.upper()),
                model_provider=ModelProvider.DASHSCOPE
            )
            
            result.update({
                'status': 'success',
                'analysis_result': analysis_result,
                'model_used': model_used,
                'processing_time': token_usage.get('processing_time', 0),
                'token_usage': token_usage.get('total_tokens', 0)
            })
            
            self.success_count += 1
            print(f"✅ 分析成功: {analysis_result[:100]}...")
            
        except Exception as e:
            result.update({
                'status': 'error',
                'error': str(e)
            })
            self.error_count += 1
            print(f"❌ 分析失败: {str(e)}")
        
        self.results.append(result)
        return result
    
    async def batch_analyze(self, file_path, analysis_type="summary", delay=1):
        """批量分析文本文件"""
        print(f"📄 解析文件: {file_path}")
        
        # 解析文件
        try:
            items = self.text_parser.parse_extracted_texts_file(file_path)
            print(f"✅ 成功解析 {len(items)} 个视频文本")
        except Exception as e:
            print(f"❌ 文件解析失败: {e}")
            return []
        
        if not items:
            print("❌ 文件中没有找到有效内容")
            return []
        
        print(f"\n🚀 开始批量分析 {len(items)} 个视频")
        print(f"🎯 分析类型: {analysis_type}")
        print(f"🤖 使用模型: 通义千问")
        print("=" * 60)
        
        start_time = time.time()
        
        # 逐个处理
        for i, item in enumerate(items, 1):
            await self.analyze_single_video(item, i, len(items), analysis_type)
            
            # 添加延时避免API限制
            if i < len(items):
                print(f"⏳ 等待 {delay} 秒...")
                await asyncio.sleep(delay)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print(f"🎉 批量分析完成！")
        print(f"📊 总计: {len(items)} 个视频")
        print(f"✅ 成功: {self.success_count} 个")
        print(f"❌ 失败: {self.error_count} 个")
        print(f"⏱️ 总耗时: {duration:.1f} 秒")
        
        return self.results
    
    def save_results(self, analysis_type, filename=None):
        """保存分析结果"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{analysis_type}_results_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"🤖 AI文本分析结果 - {analysis_type}\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"📊 统计信息:\n")
            f.write(f"   总视频数: {len(self.results)}\n")
            f.write(f"   成功处理: {self.success_count}\n")
            f.write(f"   失败数量: {self.error_count}\n")
            f.write(f"   分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("📝 分析结果:\n")
            f.write("=" * 40 + "\n\n")
            
            for result in self.results:
                if result['status'] == 'success':
                    f.write(f"【{result['video_id']}】\n")
                    f.write(f"标题: {result['title']}\n")
                    f.write(f"模型: {result['model_used']}\n")
                    f.write(f"处理时间: {result['processing_time']:.2f}秒\n")
                    f.write(f"Token使用: {result['token_usage']}\n")
                    f.write(f"分析结果:\n{result['analysis_result']}\n")
                    f.write("-" * 50 + "\n\n")
                else:
                    f.write(f"【{result['video_id']}】处理失败\n")
                    f.write(f"标题: {result['title']}\n")
                    f.write(f"错误: {result['error']}\n")
                    f.write("-" * 50 + "\n\n")
        
        print(f"💾 结果已保存到: {filename}")
        return filename

async def main():
    """主函数"""
    print("🤖 直接文本提炼工具")
    print("=" * 50)
    
    extractor = DirectTextExtractor()
    
    # 1. 检查AI服务
    print("1. 检查AI服务状态...")
    if not extractor.check_service_available():
        print("❌ 通义千问服务不可用")
        print("💡 请检查API密钥配置")
        return
    else:
        print("✅ 通义千问服务可用")
    
    # 2. 检查文件
    file_path = "extracted_texts_20250911_122444.txt"
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print(f"✅ 找到文件: {file_path}")
    
    # 3. 选择分析类型
    print(f"\n📋 选择分析类型:")
    print("1. summary - 生成摘要（推荐）")
    print("2. extract - 提取关键信息")
    print("3. keywords - 提取关键词")
    print("4. classify - 主题分类")
    print("5. sentiment - 情感分析")
    
    choice = input("请选择 (1-5，默认1): ").strip() or "1"
    
    analysis_types = {
        "1": "summary",
        "2": "extract", 
        "3": "keywords",
        "4": "classify",
        "5": "sentiment"
    }
    
    analysis_type = analysis_types.get(choice, "summary")
    
    # 4. 设置延时
    delay = float(input("请输入处理间隔秒数 (默认1秒): ").strip() or "1")
    
    # 5. 开始分析
    print(f"\n🚀 开始{analysis_type}分析...")
    results = await extractor.batch_analyze(file_path, analysis_type, delay)
    
    if results:
        # 6. 保存结果
        output_file = extractor.save_results(analysis_type)
        
        # 7. 显示预览
        print(f"\n📝 前3个结果预览:")
        print("-" * 50)
        
        success_results = [r for r in results if r['status'] == 'success']
        for i, result in enumerate(success_results[:3], 1):
            print(f"\n{i}. 【{result['video_id']}】")
            print(f"   标题: {result['title'][:60]}...")
            print(f"   结果: {result['analysis_result'][:150]}...")
        
        print(f"\n🎉 完成！查看完整结果: {output_file}")
    else:
        print("❌ 没有成功的分析结果")

if __name__ == "__main__":
    asyncio.run(main())
