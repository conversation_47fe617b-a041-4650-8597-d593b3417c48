"""
文本解析工具
专门用于解析extracted_texts文件格式
"""

import re
from typing import List, Dict, Optional
from pathlib import Path
from app.models.requests import ExtractedTextItem

class TextParser:
    """文本解析器类"""
    
    def __init__(self):
        self.separator = "-" * 80  # 分隔符
    
    def parse_extracted_texts_file(self, file_path: str) -> List[ExtractedTextItem]:
        """
        解析extracted_texts文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            解析后的文本项列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.parse_extracted_texts_content(content)
        
        except FileNotFoundError:
            raise ValueError(f"文件不存在: {file_path}")
        except UnicodeDecodeError:
            raise ValueError(f"文件编码错误，请确保文件为UTF-8编码: {file_path}")
        except Exception as e:
            raise ValueError(f"解析文件时发生错误: {str(e)}")
    
    def parse_extracted_texts_content(self, content: str) -> List[ExtractedTextItem]:
        """
        解析extracted_texts内容
        
        Args:
            content: 文件内容字符串
            
        Returns:
            解析后的文本项列表
        """
        items = []
        
        # 按分隔符分割内容
        sections = content.split(self.separator)
        
        for section in sections:
            section = section.strip()
            if not section:
                continue
                
            try:
                item = self._parse_single_section(section)
                if item:
                    items.append(item)
            except Exception as e:
                print(f"解析单个段落时出错: {str(e)}")
                continue
        
        return items
    
    def _parse_single_section(self, section: str) -> Optional[ExtractedTextItem]:
        """
        解析单个文本段落
        
        Args:
            section: 单个段落内容
            
        Returns:
            解析后的文本项，如果解析失败返回None
        """
        lines = [line.strip() for line in section.split('\n') if line.strip()]
        
        if len(lines) < 3:
            return None
        
        title = ""
        video_id = ""
        content = ""
        
        # 解析标题
        for line in lines:
            if line.startswith("标题:"):
                title = line.replace("标题:", "").strip()
                break
        
        # 解析视频ID
        for line in lines:
            if line.startswith("视频ID:"):
                video_id = line.replace("视频ID:", "").strip()
                break
        
        # 解析文字内容
        content_start = False
        content_lines = []
        
        for line in lines:
            if line.startswith("文字内容:"):
                content_start = True
                # 获取冒号后的内容
                first_content = line.replace("文字内容:", "").strip()
                if first_content:
                    content_lines.append(first_content)
                continue
            
            if content_start:
                content_lines.append(line)
        
        content = " ".join(content_lines).strip()
        
        # 验证必要字段
        if not title or not video_id or not content:
            return None
        
        return ExtractedTextItem(
            title=title,
            video_id=video_id,
            content=content
        )
    
    def get_text_statistics(self, items: List[ExtractedTextItem]) -> Dict[str, any]:
        """
        获取文本统计信息
        
        Args:
            items: 文本项列表
            
        Returns:
            统计信息字典
        """
        if not items:
            return {
                "total_videos": 0,
                "total_characters": 0,
                "average_length": 0,
                "longest_text": 0,
                "shortest_text": 0
            }
        
        lengths = [len(item.content) for item in items]
        
        return {
            "total_videos": len(items),
            "total_characters": sum(lengths),
            "average_length": sum(lengths) // len(lengths),
            "longest_text": max(lengths),
            "shortest_text": min(lengths)
        }
    
    def filter_by_keywords(self, items: List[ExtractedTextItem], keywords: List[str]) -> List[ExtractedTextItem]:
        """
        根据关键词过滤文本项
        
        Args:
            items: 文本项列表
            keywords: 关键词列表
            
        Returns:
            过滤后的文本项列表
        """
        if not keywords:
            return items
        
        filtered_items = []
        
        for item in items:
            # 检查标题和内容中是否包含任何关键词
            text_to_search = f"{item.title} {item.content}".lower()
            
            if any(keyword.lower() in text_to_search for keyword in keywords):
                filtered_items.append(item)
        
        return filtered_items
    
    def combine_texts(self, items: List[ExtractedTextItem], max_length: int = 50000) -> List[str]:
        """
        将多个文本项合并为适合AI处理的文本块
        
        Args:
            items: 文本项列表
            max_length: 单个文本块的最大长度
            
        Returns:
            合并后的文本块列表
        """
        combined_texts = []
        current_text = ""
        
        for item in items:
            # 格式化单个项目
            formatted_item = f"标题: {item.title}\n内容: {item.content}\n\n"
            
            # 如果添加当前项目会超过长度限制，先保存当前文本块
            if current_text and len(current_text) + len(formatted_item) > max_length:
                combined_texts.append(current_text.strip())
                current_text = formatted_item
            else:
                current_text += formatted_item
        
        # 添加最后一个文本块
        if current_text:
            combined_texts.append(current_text.strip())
        
        return combined_texts
