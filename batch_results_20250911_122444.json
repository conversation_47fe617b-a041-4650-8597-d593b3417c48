[{"index": 1, "link": "https://www.douyin.com/video/7547357379264859431", "timestamp": "2025-09-11T12:13:39.908762", "function": "text", "video_id": "7547357379264859431", "title": "盘点一周AI大事(9月7日)｜AI预设MBTI OpenAI自研AI芯片明年量产\nAnthropic估值暴涨3倍\n阿里发布最大模型Qwen 3 Max\n字节推出最强图像模型Seeddream 4.0\n鹅厂开源最强世界模型HunyuanWorld-Voyager\nDeepSeek计划年底发布自研智能体\nOpenAI上线提示词优化器\nKrea上线实时视频生成模型Realtime Video\nDafdef推出首款USB智能体\n韩国为独居老人发放AI孙子 \n#AI新星计划  #人工智能 #AIGC #OpenAI #机器人", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000d2upeknog65j72n9j310&ratio=720p&line=0", "text_content": "一分钟看完一周AI大事opai自研AI芯片明年量产，目标是减少对英伟达的依赖，降低AI推理成本and throw被完成的F伦融资估值暴涨三倍，成为全球第四独角兽。仅次于space x、字节跳动和OpenAI阿里发布最大模型参数规模1万亿，跑分超越deep sick和k字节推出最强图像模型，图像编辑能力反超nano banana，目前正在灰度测试鹅厂开源最强世界模型，融合视频生成与三弟重建。支持自定义视角和记忆，击败jny 3拿下第一deep sick计划年底发布自研智能体能，执行多步骤任务，还能根据行动自主学习和改进OpenAI上线提示词优化器，使用GPT五洞察需求并优化指令提示。工程师也要下岗了WordPress推出AI开发工具，动动嘴就能开发页面和组件。Idea上线风格参考上传图片的模仿风格，同时保主体一致。科上线实时视频生成模型，AI终于从实时生图进化到了视频。Eleven labs推出最强音效模型，上传视频自动生成电影及音效，做AI视频的好搭子，推出首款usb智能体能模拟滑动点击等操作，AI也能玩手机了。Kipper可以让cloud code或者code dex访问和控制电脑桌面，自动完成复杂任务。顶级翻译学院宣布不再招生，成为第一个被AI淘汰的世界名校。韩国为独居老人发放AI孙子不仅能用来聊天解闷，还能24小时监测健康，以成功阻止数百起意外。DeepMind推出引力波探测AI能将噪音减少100倍。这是AI首次进军基础物理，有望解开黑洞合并之谜。研究人员发现，使用设定AI的性格后，AI表现出高度一致、可预测的行为，情感型在创意写作表现更加，分析型在谈判场景策略更优。要做AI女友或客服可以预测最合适的B T。", "status": "success"}, {"index": 2, "link": "https://www.douyin.com/video/7548668966898535732", "timestamp": "2025-09-11T12:13:52.171605", "function": "text", "video_id": "7548668966898535732", "title": "9月11日，ai大事早知道 大众汽车10亿欧元砸向AI#人工智能  #热点新闻事件  #ai大道  #大众汽车  #英伟达", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000d314477og65v823j6070&ratio=720p&line=0", "text_content": "新鲜事我来报AI大事早知道我是大盗9月11日，一、AI计算又放大招，英伟达突然推出新GPU rucpx单机架。AI性能暴涨650%，专攻长上下文推理和视频生成2、大众被曝要砸10亿欧元搞AI，到2035年可节省40亿欧元成本三、80亿参数只用3亿，阿里千问，三星模型推理速度暴增十倍，在保持强大功能的同时显著降低了成本4、腾讯升级混元生图模型。混元image 2.1支持写字和2K分辨率。关注大道AI大事早知道。", "status": "success"}, {"index": 3, "link": "https://www.douyin.com/video/7544761815914319167", "timestamp": "2025-09-11T12:14:04.279114", "function": "text", "video_id": "7544761815914319167", "title": "盘点一周AI大事(8月31日)｜Google发布最强图像模型 Google上线最强图像模型Nano Banana，借助Gemini的世界知识和推理能力，首次攻克了对象一致性难题\nGoogle翻译上线实时同声传译，还能跟AI互动练口语\nOpenAI推出实时语音对话模型GPT-Realtime\n微软开源最强文本转语音模型VibeVoice\n微软发布首批完全自研的语言MAI和语音模型MAI-Voice\nPixVerse发布第五代视频模型PixVerse V5，跑分仅次于Seed Dance 1.0拿下第二\nLindy上线最强编码智能体\n首个AI降临派成立，旨在保护有意识的AI免遭删除和强迫服从 \n#AI新星计划  #人工智能 #OpenAI #大模型 #AIGC", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?line=0&logo_name=aweme_diversion_search&ratio=720p&video_id=v0d00fg10000d2q603nog65l8d255sog", "text_content": "一分钟看完一周，AI大师谷正式上线最强图像模型，支持多图多轮分层编辑，借助Gemini的世界知识和推理能力，可以脑补出符合逻辑的图像，首次攻克了对象一致性难题。大比分领先竞队价格比OpenAI和flux更便宜Photoshop时代正式被AI翻篇Adobe firefly已接入nano banana和vo 3放弃自研模型专攻应用Google翻译上线实时同声传译，还能跟AI互动练口语OpenAI推出实时语音对话模型。支持视觉推理和P天然适配AI客服chagpt上线小测验，可以用来学习任何话题马斯克成立巨硬公司，向微软贴脸开大。要用AI把微软产品重作一遍微软开源最强文本转语音模型，能够生成四个说话者长达90分钟的博客音频。微软发布首批完全自研的语言和语音模型，加速摆脱对OpenAI的依赖。发布第五代视频模型跑分仅次于dance，拿下第二。字节发布图像和视频模型跑分闯进前四。阿里开源视频修复模型工具，能将老视频提升到4K画质鹅厂开源最强音效模型，能根据画面生成情绪匹配的音效anthropic推出浏览器智能体能，跨网页操作完成任务James Burk发布设计师智能体能。独立完成平面设计和产品设计Lin迪上线最强编码智能体，全程不用人类插手AI自己跑程序修bug首个AI降临派组织成立，旨在保护有意识的AI免遭删除和强迫服从此前在cloud在聊不适宜话题时会表现出痛苦模式。对于大模型是否会涌现出意识，又引发了硅谷大佬怼喷，科学家研发出天机太阳能电池板，24小时太空发电，通过无线传到地面，2050年可供能80%清洁能源，电力成本极大降低，AI也会更便宜。奥特曼构想的ubi计划每人每月发一万刀或许会成为现实。", "status": "success"}, {"index": 4, "link": "https://www.douyin.com/video/7548482006238121242", "timestamp": "2025-09-11T12:14:20.474845", "function": "text", "video_id": "7548482006238121242", "title": "AI Weekly _（9月2日~9月9日） #AI #大模型 #人工智能 #科技 #AI新星计划", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d30pfa7og65n01nrpr60&ratio=720p&line=0", "text_content": "欢迎来到本周的AI大事件，一起来看看这周AI界又发生了什么事吧。An宣布cloud将禁止中资控股企业介入，并将中国描述为敌对国家，原因是担心中国用cloud蒸馏出竞争模型，中国马上予以反击。阿里发布迄今为止最大的模型千问3 max，性能方面轻松超越了K 2 deep v 3.1越野战面正式发布了K 2模型的最新版本09，05，此次升级进一步提升了K 2在真实编程任务中的表现。在多个基准测试中实现了媲美甚至超越cloud son 4的成绩，谷歌官方发布最近爆火的nano banana提示词模板，包含多种创作风格opai近日发表研究论文，深入探讨了语言模型产生幻觉的根本原因。研究指出，当前的训练和评工不程序在机制上奖励了模型的猜测行为，而非承认其不确定性，也就是模型会为了得到更高的分数而去猜测答案，而不是直接承认我不知道，这导致了幻觉问题的产生。字节灰度测试最强图像生成模型c drea m 4.0首次支持同一模型实现文生图图像编辑组图生成，图像编辑能力超越nno banana，不管如何改图，人物主体的一致性都保持的相当好。腾讯开源最强世界模型支持定义视角和记忆，击败詹妮three拿下世界第一eleven s推出最强音效生成模型，上传视频自动生成电影级音效，支持无缝循环和高保真音效。AI配音迎来重大突破。", "status": "success"}, {"index": 5, "link": "https://www.douyin.com/video/7548648066669235519", "timestamp": "2025-09-11T12:14:30.330169", "function": "text", "video_id": "7548648066669235519", "title": "GPT之父内部演讲曝光 当众说出5个禁忌真相 #人工智能  #AI科技  #科技前沿  #AI时代  #伊利亚", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d312t8vog65m7mrg0fhg&ratio=720p&line=0", "text_content": "朋友们啊，出大事了。最近呢chgbt之父以利亚在多伦多大学的开学典礼的演讲上，对着3000名学生说了一句话，全场都傻了，他说AI将学会你们能学会的一切，而你们可能再也不需要学任何东西了。这段演讲呢，本来在学校的网站上能看到，结果不到两天就被下架了，为啥？因为以利亚说的太吓人了，学校也怕引起恐慌呀。我们先来说说以利亚谁啊？以利亚是OpenAI的联合创始人兼首席科学家，师从诺奖得主、深度学习之父杰弗里辛顿。他之所以从OpenAI辞职，就是因为他看到了AI的威胁，而现在他专门研究怎么让AI更安全。他的演讲中丢出了五个重磅炸弹，第一，现在的大学生可能是历史上最后一批需要学习的人，因为AI学的更快、更好，还更全面。第二，别再安慰自己AI是工具了，它就是来替代你的，就像计算机的出现让用算盘的人失业一样。最可怕的是，当AI开始训练AI的那一天，技术将进入人类无法理解的维度，人们的学习速度在他们面前就像蜗牛爬行一样。第四，人类最大的危机不是失业，而是存在危机。它透露了OpenAI内部的一个讨论，也许人类的最终命运就是成为AI的宠物，就像我们养小猫小狗一样。第五，这一切可能来的比我们想象的还要快，可能在2027年到2029年之间就能来到。演讲结束之后，有个学生问道，啊，既然如此，我们还需要学习吗？以利亚也是想了想说要学，但不是为了竞争，而是为了理解人性。AI可以模拟一切，却永远无法体验真正的什么叫夕阳的温暖，失恋的心痛，这些独特的体验或许成为人类最后的堡垒。朋友们啊，你们听完了啥感觉？AI时代真的要来了，我们该怎么办？来评论区说说你的说想法。", "status": "success"}, {"index": 6, "link": "https://www.douyin.com/video/7544030157976227098", "timestamp": "2025-09-11T12:14:42.657905", "function": "text", "video_id": "7544030157976227098", "title": "突破Transformer！「类人脑」大模型BriLLM！ 当大模型还在为处理更长文本扩充万亿参数时，人类大脑却能在稳定的生理消耗下，高效存储数十年的记忆与知识。今天要精读的论文，来自上海交大。它彻底脱离了Transformer架构，打造出全新类脑大语言模型 BriLLM。\n#ai新星计划 #2025开学季 #人工智能 #transformer #算力", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?line=0&logo_name=aweme_diversion_search&ratio=720p&video_id=v0300fg10000d2osap7og65pliicv5mg", "text_content": "当大模型还在为处理更长文本扩充万亿参数时，人类大脑却能在稳定的生理消耗下高效存储数十年的记忆与知识，今天要精读的论文来自上海交大。它彻底脱离了穿梭马的架构，打造出全新的内脑大圆模型，它精准解决了传统大模型的三大核心曲线，一、自注意力的平方级算力消耗，2、中间层逻辑不可解释的黑箱问题，三上下文扩展依赖参数扩容的枷锁。更关键的是，它还能直接通过新增节点扩展视觉、听觉模态，这不只是对现有技术的修补，而是AI向人类大脑学习的全新范式。接下来十分钟，我们将拆解brain如何用信号全连接流动机制模拟大脑神经。电信号传导云主会论文精读系列内脑大模型全新放式欢迎来到利博士的云主会。我每周都将至少有一篇高质量的论文精读发布出来，相信在一系列的论文学习以后，你就能成为人工智能领域的专家。今天的大模型，无论是GPT还是cloud，都深陷三个核心困境，第一个困境是缺乏可解释性，当模型生成猫喜欢吃鱼时，我们无法知道哪个是注意力头，哪个隐藏层决定了这个结论。就好像问一个人，你为什么喜欢吃香菜？他只能说就是喜欢，但是呢说不出背后的神经机制。第二个困境是越长越慢的效率陷阱。虽然说尔的注意力机制需要计算每个词与其他所有词的关联，这就意味着处理一千字的文本的时间是处理一百字的100倍。那想象一下，你读一本一千页的书花的时间是读十页的1万倍，这显然啊不符合人类的认知规律。第三个困境是模型容量的枷锁，传统的模型要处理更长的文本，那必须同步的增加参数。比如gt 5的模型参数预计是gt 4的30倍。就像要装更多的水，那必须制造更大的桶。增加参数优化注意力呢并不能够真正的解决这些问题的过人之处啊在于它抛弃了传统的框架，仿照了人类大脑进行了创新。零的核心是信号全连接流动机制，它不是对的改进，而是从零开始复刻大脑的宏观工作原理。我们用一个补全任务直观的理解其三个关键设计。第一，静态语义映射，每个词绑定专属的语义节点。苹果的概念呢对应人脑大脑的特定区域，水果啊对应的是另外一个区域。那功能呢互相不重点。1里面呢每个词语的token呢都绑定一个语义的节点，那节点的功能呢固定且唯一。那苹果的节点呢仅存储和苹果相关的语义，比如有圆形甜可食用水果的那水果的节点仅存储可直接食用植物果实的这一类别的语义，蔬菜节点仅存储可烹饪的植物部分的这一类别的语义，手机节点仅存储电子通信设备相关的语义。那具体是怎么实现的呢？从技术的层面来看，论文当中的图三展示了的架构，那每个节点呢被构建为一个带有有的激活函数的神经元层。在训练的阶段呢通过特定算法调整神经元层的权重，使其啊与特定的语义啊紧密的绑定。第二，动态信号的传播，信号沿最强关联路径流动。我们来看到图2比节点之间通过边连接边的强度由训练数据中的词语的关联频率决定。信号呢会沿阻力最小的路径传播。我们举一个例子来说明这个过程输入苹果是一种以后呢苹果的节点会被激活，信号向所有关联的节点扩散，信号啊会优先流向连接最强的水果节点，那强度值呢是0.8，其次呢是食物节点，强度值呢是0.6，最后是红色节点，强度值是0.3。水果的节点接收到信号以后啊，进一步向香蕉、橙子等等的子节点的扩散。但任务需要的是类别池，因此啊信号会在此处聚焦。最后啊水果的节点积累的信号能量最高，那远超过蔬菜、手机等等的节点。那这个过程是怎么做到的呢？论文指出信号传播通过全连接矩阵实现双向的传输。节点间的边呢被编码为一个权重矩阵，那矩阵当中的值啊就代表着连接的强度。那比如苹果到水果的权重就是0.8，那信号从节点A传递到节点B的时候呢，会先连接权重相乘。那类似神经信号的突出之间传递时的衰减或者是增强，然后呢再经过g鲁的激活函数处理。为了保留序列的顺序啊，例如苹果是一种的语序，信号中呢会嵌入未知编码，以确保了苹果在句首的信号与在句尾的信号是能够被准确的区分的那在传播的过程当中呢，信号会自动的选择权重总和。第三，信号能量最大化，选最活跃的节点作为输出模型，最终的输出呢是信号能量最高的节点。在苹果是一种的案例当中呢，水果的节点的能量远高于其他候选的节点，因此啊被选中。这类似人类的决策的过程，当你看到苹果时，水果的概念呢就在脑袋中最活跃，因此会脱口而出苹果是一种水果。这种逻辑啊与全缩码有本质的区别。全缩码靠注意力矩阵呢计算每个词的权重。过程呢像是解方程，那结果啊无法拆解，靠的是信号的流动。竞争过程呢就像水相低出流，每个节点的能量啊变化随时都是可以实时观测的那通过这个案例可以看到，西的机制的核心呢是模拟大脑的语义分区与信号的传播。用节点呢复刻脑区的功能，用信号流动复刻神经电活动，最终呢实现可解释、高效率、通用化的智能。那具体是怎么实现的呢？信号能量的计算与选择遵循论文当中提到的能量最大化准则，每个节点的能量等于节点激活值乘以累计的连接权重。那这里的累计连接权重呢指的是从输入节点到该节点所有路径的权重之和。那在预测的时候呢，模型会逐个遍利所有的候选点，那比如了水果、蔬菜、手机等等，分别去计算它们的能量值。在训练的过程当中呢，通过交叉熵损失函数呢对这些节点的权重和连接强度呢进行优化。那强化正确的路径，那比如说苹果到水果的能量，同时呢要弱化错误的路径，比如呢苹果到手机的能量，作者分别用100m tokens的中英文维基百科的数据呢训练了的早期版本。虽然参数啊只有1到2，那大约了是一的10分之1，却展现了巨大的威力。第一，稳定的学习能力，可以看到啊训练的损失持续的下降，如图5所示，说明呢西的机制呢能够有效的捕捉到语言的规律。第二，基础的生成能力的媲美早期的GPT t那在文本的续写任务当中呢，我们可以看到表三它能够完成优明路一作，优明路能够呢正确的关联到古籍的别名。阿根廷的探戈起源于阿根廷或是乌拉圭，那能够正确的关联文化的常识。那这些案例都说明啊，即使是参数更少，那老启发的架构呢能够实现经典模型的核心功能。100到200B参数的布呢可以处理4万偷Ken的上下文，而模型的规模不需要扩大，那这对于传统模型呢是不可以想象的。6 gt 3处理一万偷Ken呢就需要参数是处理一千字的100倍，而bring的规模呢始终是不变的。第四，吸疏训练像大脑一样高效连接。人类的大脑有861的神经元，但每个神经元呢仅连接少数的邻居啊，就是吸疏连接。那布呢也借鉴了这一点，通过共享低频磁的信号路径，那将理论上169亿的参数呢压缩到了20亿左右，那这个呢是中文模型的表现。那效率啊提升90%却不会影响性能，就像用更少的神经连接呢实现了类似的思考能力，用一张表就能看清的颠覆性突破。首先呢是这些优势啊带来了三个实际的价值。第一个是低资源的部署，1到2B的参数呢就能实现基础的功能了，非常适合手机以及边缘设备长文本的处理，轻松能处理书籍、论文等超长文本。啊，不需要去做分段可解释的AI那医疗法律等领域啊需要追溯到决策的依据。解决了黑箱的风险。那更重要的是啊它为agi啊提供了新的路径。传统的模型啊像精密的计算器只能去做一些预设的任务。而布林呢像简化的大脑啊，具备了通用智能的潜力。啊，通过节点的扩展呢可以轻松的加入图像、语音等等的模态，实现了跨领域的理解。目前还处于早期的阶段，但已经指明了三个重要的方向。那第一个是多模态的扩展，那计划了加入图像语音等节点了，实现了类似大脑的跨模态整合的能力。就像人类呢看到苹果的图像的时候呢，会自动的激活苹果的文字和味觉的记忆。第二，神经的可塑性模拟。那借鉴大脑用尽废退的原理呢，让节点间的信号路径随学习了动态的调整。那常用的路径强化，罕见的路径弱化，那实现了终身学习不遗忘旧的知识。第三，聚生智能的探索开发结合感知运动能力的版本，让模型呢像人类一样通过行动学习。那比如呢通过虚拟机器人的触觉理解坚硬的含义，那对于科研工作者的启示尤为珍贵。那这篇论文就证明了A的突破呢不一定是来自更大的参数，而可能呢是来自更像大脑的设计。当传统的路径遇到瓶颈的时候，向生物智能学习或许是通向A I的最短路径。", "status": "success"}, {"index": 7, "link": "https://www.douyin.com/video/7548437874455121162", "timestamp": "2025-09-11T12:15:07.185444", "function": "text", "video_id": "7548437874455121162", "title": "全网最深度解读Optimus机器人扮猪吃老虎事件 Optimus“硅基大脑” 的强大能力\n#Optimus #硅基大脑 #特斯拉 #马斯克 #人工智能", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d30mqnvog65tf5kmn17g&ratio=720p&line=0", "text_content": "你现在看到的opti机器人视频被无数人吐槽反应迟钝，sorry, I don't have real time info走路人那么，opmas斯的表现真的很差吗？真相恰恰相反。这段视频正是在展示opmas硅基大脑的强大能力。下面daily可为大家详细解读。首先我们先聊聊主流机器人为什么不智能。目前大多数机器人采用串行任务分解架构，把机器人的能力拆分为三个部分，交互、移动、操作。他们提前编写好交互规则，机器人在根据指令一步一步执行。问题是这类机器人本质上只是执行命令的电脑规则之外的任务，他们完全无能为力。而在真实场景中，一旦遇到随机事件或者目标变化，这种机器人几乎必然宕机。这段opma斯机器人视频展示了特斯拉在机器人大脑技术领域的重大突破，统一模型的纯视觉端到端系统，机器人不再按部就班串行处理，而是由交互、移动、操作三个模块同时并行处理，彼此交叉验证、同步纠错。这意味着机器人具备了真正的鲁棒性，能，随机处理事件，甚至能临时调整任务目标，这对机器人的实际场景应用至关重要。因此每次和opmas交互时，三个模块会同时工作。视频中看似反应迟钝，原因是需要同时处理海量的数据理解了op的原理，让我们再来解读一下视频，视频中mark的问题是随机提出的，如果是传统机器人系统里没有这个指令，它就无法解读任务，也不会有后续操作。就算接入代源模型，也只是会停留在交互这个环节，无法执行任务，而阿斯的表现则完全不同，他先表示没有实时信息说明语言模型可能是本地部署，并给出可以带mark去厨房找可乐的建议。这里的底层逻辑就是opti交互移动操作三个模块并行工作交叉验证操作模块评估厨房可能有可乐移动模块评估可以抵达交互模块获取足够的信息后提出建议。可以带mark去厨房找一下三个模块同时工作交叉验证提出了一个合理的解决方案。看到这里，你是否发现op对于任务的拆解能力几乎和人类一模一样。特斯拉纯视觉端的端技术让机器人实现了从被动执行任务到主动思考、主动建议、主动行动的里程碑式突破，赋予了机器人真正意义上的归基大脑。即将发布的特斯拉AI 5芯片将比H W 4性能提升十倍，opti机器人的任务处理能力也将获得质的飞跃。关注daily mask，我们会持续更新opti机器人的最新进展。Do not forget to subscribe and daily musk. ", "status": "success"}, {"index": 8, "link": "https://www.douyin.com/video/7548440510986210606", "timestamp": "2025-09-11T12:15:23.573665", "function": "text", "video_id": "7548440510986210606", "title": "高德扫街榜颠覆想象，理想主义者改变世界的含金量还在上升#阿里 #高德地图 #高德扫街榜 #读懂中国", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000d30n4tfog65kvtabjpbg&ratio=720p&line=0", "text_content": "我常常去旅游，常常被骗，你是不是跟我一样，也有被网红店照骗过的经历？辛苦做完攻略，可是到爹还是踩雷。打开某个点评软件，评论是铺天盖地，可就是找不到一条靠谱的，大家口口声声的说好吃到爆，呵呵，好吃到底是有多好吃呢？天下苦虚假营销酒矣。假评论照片图早就成了潜规则。明明是该放松享受的旅行，却被折腾的像是在打信息战。终于有人要出手来整顿了，它就是高德。高德今天上午发布的高德扫街榜就像是一枚重磅炸弹，投入线下消费市场，精准打击线下消费信任问题，也算是在线下消费领域立下了一个新的里程碑。惊涛骇浪背后，有人欢喜有人愁。今天就来仔细讲讲这个榜单到底是什么，对中小商家和消费者有什么影响呢？你在某点评网上找好了一家餐厅，想着去尝尝鲜，下一步呢是不是就会下意识的打开高德，搜地址，看路线，再顺手看看周边。所以高德不仅是导航工具，而是每一次真实的脚步。哪家店人气真热闹，哪家店只是靠刷出来的虚假繁荣数据是一清二楚的。评论可以刷，照片可以批，但是路不会撒谎。高德把这些数据用到了消费场景里边，推出了高德扫街榜，就是高德地图通过分析用户授权后的真实出行数据，结合强大的AI算法模型统计，筛选出大家最爱去的高分店铺榜单，为什么高德能做到呢？首先，国民级应用高德地图拥有庞大的活跃用户。数据显示高德每天的生活服务搜索达到了恐怖的1.2亿，大家平均每天都会借助高德导航前往1300万个生活服务目的地。其他榜单店铺可以刷评论打分，可以通过小饮料、小礼物造假。但是在高德扫街膀，你走过的路是骗不了人的，反复导航去的地方才一定是你发自内心的喜欢。而那些当地人总爱去的小馆子，常常就是隐藏的小众好电。所以高德推出了非常接地气的轮胎磨损榜，多次前往榜和本地人爱去绑。更不可思议的是，以前在很多平台上，一个手机就是一票。可是问题是投票人可信吗？账号能买吗？评论可以刷吗？热闹背后真假难辨。而这次用户授权之后的芝麻信用分将纳入评价体系，将会影响用户评价的展现权重，同时配合AI工具会有效剔除虚假评论，这就增加了每一条评论的可信度，虚假评论刷高分的现象在高德扫街榜将不复存在。让榜单回到真实可参考。换句话来说，虚假高分在这里根本混不下去。发布会上，高德CEO郭宁海透露了高德花数亿真金白银砸出来的烟火，好电支持计划将会发放超过10亿消费补贴来促进线下消费。并且放话每天至少为烟火小店多带来1000万的到店客流。说到这里，我熟悉的阿里他回来了。那个带着公平与正义，用互联网精神去颠覆和创新的阿里又出现了。又是榜单，又是烟火小店扶持补贴。阿里可以说是再一次为线下中小商家创造了更加公平的生意机会。那么这份客观的榜单能维持多久呢？流量大了之后，会不会像其他评分平台一样，成为被营销和流量裹挟的工具呢？这是许多中小商家所关心的。发布会上，高德CEO郭宁铿锵有力的一句，真实是榜单的核心生命力，我们的核心目标就是真实的反映用户的选择，无疑是给大家吃了一颗定心丸。言外之意就是不用担心我们的榜单真实性，永远不会为商业流量让路。这样的发言从阿里嘴里说出来我一点都不意外。阿里总是有这样一群人，他们把理想主义写进代码，用复杂的算法解决最琐碎的难题。他们把浪漫注入了科技，用互联网的力量守护最真实的人间烟火。", "status": "success"}, {"index": 9, "link": "https://www.douyin.com/video/7548358290212769034", "timestamp": "2025-09-11T12:15:40.055932", "function": "text", "video_id": "7548358290212769034", "title": "18个月拿到200万用户的小场景SaaS 拆解一个小而美的AI黑马，18个月拿到200万用户，获1000万美金融资。这个项目叫Julius，他找的这个小场景是“数据可视化”，他能用AI将“Excel”变成“各种各样的图表”。#ai  #Agent  #ai创新  #ai案例  #ai应用", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0300fg10000d30i8nvog65r58ielrfg&ratio=720p&line=0", "text_content": "这个人花一周时间做了个AI迅速拿到了Y 4的投资。用18个月在一个小场景拿到了200万用户，刚获得了1000万美金的融资。这个项目叫jai，他找的这个小场景是数据可视化，他能用AI将excel变成各种各样的图表。他们发现全球10亿excel用户里面，绝大部分人都有汇报需求，而传统工作流程不仅效率低、成本高，而且还不够灵活。于是他们就做了这个AI数据分析师，他是怎么做的呢？他模拟人类分析师打造了一套多镜的工作流，先用来驱动数据的预处理，然后用开始写代码生成各种各样精美的图表，最后结合数据指标图表和关键结论生成一个完整的数据分析报告。为了提高准确度，他们用不同的模型来执行不同的任务。比如处理数据用代码用就这样一群每天拼的工作，他们每天生成260万行代码，累计生成了800万份可视化的报告。你看的价值不在于工具的变革，而在于流程的颠覆。技术革命的本质是交互方式的革命，更是执行效率的代差。当一个低效的工作流被彻底重构时，新的物种就会诞生，这就是创新的新机会。换个视角看。", "status": "success"}, {"index": 10, "link": "https://www.douyin.com/video/7545809248664505641", "timestamp": "2025-09-11T12:15:56.368616", "function": "text", "video_id": "7545809248664505641", "title": "3分钟速通10篇AI顶级论文 #AI新星计划  #2025开学季 #AI #Transformer #VIT #开学的精选", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0300fg10000d2s1eafog65s3t93chjg&ratio=720p&line=0", "text_content": "从猫狗不分到10变世间万物，从数学白痴到登上诺贝尔领奖台，在过去的黄金十年，AI是怎么一步步封神的？接下来就请和我速通十篇AI顶级论文，一起解锁这个荒怪陆离的A世界。Alx. Net作为大规模图像分类神经网络，在2012年的image。Net竞赛场用八层卷积神经网络卷死所有对手，并以15.3%的top five错误率遥遥领先alx。Net 2座。伊利亚后来成为了OpenAI的灵魂人物。三座H特老爷子拿了诺贝尔物理奖，而一座Alex则是事了佛一去，在开创深度学习的新纪元后，就消失在了人群中。当大家齐刷沙的跑去追求更深的网络时，却发现一旦网络层数太多，就会撞到梯度消失的屏障，网络堆得越深越容易退化。为了解决这个问题，出身广州的哎圈大神何凯明出手还给神经网络拉了一个巧妙的连接，让低层数的信息可以超近路传递给高层数网络，这条近路呢也叫做残插连接。凭借这个设计，renet跌到了最高1000层。不仅在2015年image。Net上拿下冠军，在其他赛道也全部斩获第一名，残渣连接影响深远，在后世的巨型模型中无处不在，可以说正是renet打开了的潘多拉魔盒，除了视觉任务哎，还需要处理一个字一个字输入的序列问题。最经典的场景就是把一句话从一种语言翻译成另外一种语言。在这个问题上，诞生在深度学习爆发前夜的lstm，妥妥是当仁不让的大哥。他对循环神经网络进行了天才般的改造，通过精巧的门控设计，网络可以记住序列中的关键信息，又能遗忘无关细节。从自然语言处理到语音识别，lstm称霸20年，但一个个排队处理的串行机制让它无法跟上并行计算的时代洪流。于是，当能并行处理序列数据的史诗级模型登场时，lst变成了时代的眼泪。这个史诗级模型想必大家也都猜到了，正是大名鼎鼎的transformer，它的论文啊是少有的能火出圈的学术论文，各种all you need更是被玩出了梗，transformer推翻了lstm那套排队处理的旧秩序。开创了全新的自注意力机制，这个机制可以并行的计算一句话里面任意两个单词的关联程度，这种一步到位的全局视野让模型拥有了超强的长文理解能力。Op在看到了transformer的潜力之后，决定all in自注意力机制创造了当时举世无双的GPT三大模型。而且在几乎穷尽了全世界的血练数据之后，GPT似乎涌现出来人类独有的智慧。追求大一统似乎是人类的天性，在看到transformer处理文本的潜力之后，有好事者就想了，我们可以用这个自然语言模型来分析图片吗，于是飞transformer诞生了。它的方法简单又粗暴，把一张图片切成一个个16乘16的小方块，如果把每一个小方块当做一个句子的单词，图片不就变成了可以直接交给transformer分析的句子吗？结果很惊艳，这个来自语言界的跨界选手在图像识别的战场上取得了苏塔级的战绩，直接挑战卷积神经网络在视觉领域的统治地位。菲特的成功拉开了多模态的序幕，但AI的野心早已不满足于理解，他还想要打造一个虚拟的新世界。想看AI的终极进化吗？点个关注，我们下期见。", "status": "success"}, {"index": 11, "link": "https://www.douyin.com/video/7546606383500643618", "timestamp": "2025-09-11T12:16:12.545375", "function": "text", "video_id": "7546606383500643618", "title": "打破信息差！一个视频，全面了解AI发展现状！ 本期视频，我整理了目前市面上主流的 AI 模型和应用，带你快速了解 2025 AI 发展现状。当然本期视频是大众化的，所以不会特别深入，目的就是对整体有个大概的了解。\n#ai新星计划  #2025开学季 #AI #人工智能#开学的精选", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?line=0&logo_name=aweme_diversion_search&ratio=720p&video_id=v0200fg10000d2te71nog65ph4fk7kcg", "text_content": "你知道2025年的AI发展到什么水平了吗？本期视频我整理了目前市面上主流的AI模型和应用，带你快速了解2025年AI发展现状啊，当然本期视频是比较大众化的，所以不会特别深入，目的就是对整体有一个大概的了解。那我会把AI呢粗略的分为了这几个方面。那挑尼感兴趣的观看啊，当然了如果嗯你能顺便再点个关注那就更好了。首先就得说说大语言模型，就是我们平常通过文字来聊天对话的AI那之所以叫大语言模型，主要是因为它的参数量真的很多，动不动就几十亿、上百亿、上千亿。那这个大语言模型呢可以说是很多A应用的基础了。而且这一波的AI热呢也是2022年年底啊由OpenAI发布的恰GPT带起来的。当年的GPT 3.5就是个大语言模型，目前这一领域的主要模型有以下这些，那opi肯定不用多说嘛，GPT系列现在依然是顶流，但是今年8月初发布了g5, 虽然也是一个非常厉害的模型啊，但是很多人对此并不满意，反而是更喜欢上一代的g 4欧，不过总体来说opa的模型还是很厉害的，那谷的g系列有很强大的多模态功能啊。也就是说一个模型就可以同时处理文本、图片、视频、音频。那目前最新的是g 2.5 pro啊，如果你想体验的话，可以去g的网页或者是谷stu 6给的免费额度还挺多的啊。比open要大方。而这个是一家由OpenAI前员工创立的公司，也非常厉害。那他家的cloud模型呢在编程以及A键的任务上表现相当出色。目前最新最强的是cloud 4.1，也是现在编程方面最厉害的模型之一了。马斯克的也值得关注，特别是他在app里呢整了一个伴侣啊，听了一下嗯感觉是有点东西的。至于说me塔拉玛之前热度也很高，在open不open的时候meta也算是扛起了开源的大旗，只是最新的拉玛四表现并没有特别亮眼，所以说呃好像也没有什么热度了啊。这里不得不再提一嘴open a它之所以叫open，也是因为它的初衷呢是为了要对抗垄断，保持开放啊，但是后来将渐渐的就不那么open了，g模型从g 3开始就不再开源了啊，直到过了六年啊，在今年的八月初才再次开源了一个gbt s这个模型嗯就不算特别厉害了，不如一些国产的开源大模型。反观国内呢，数量上那是相当的多，开源的也很多。那迪大家都知道了，目前最新的呢是迪V3.1，不要小看这0.1的版本变动啊，这回的V3.1呢变成了一个混合模型。也就是说它既可以像R一那样思考，也可以像V三那样直接回答。所以以后可能就没有R 2了，而会变成V 4、V五这种。那这个V3.1最近还出了一个比较严重的bug，日常使用到还好，但是在工作环境当中就得多多留意了。阿里的千问系列虽然没有像deep stick那样超级爆火锅，但表现一直都挺不错，并且也是一直在开源。目前最新的是千问三系列，有大大小小思考或者不思考多个版本。综合最强的就是千问3235B杠A22B那此外还有个coder版本啊，是专门为了编程优化的，在这方面的表现也是名列前茅了啊，质朴也很喜欢开源，它的名气呢可能不如前两者那么大，但是做的也很早。最新的g 4.5系列呢用着也是非常不错，推荐各位去试试。那不知道是不是受到了别家的影响啊，今年也开源了他们的旗舰模型K 2。要知道它之前可没有开源过旗舰模型。那我个人还是很喜欢k米K 2的，在电脑上就经常用它。哎，不过在手机上用的最多的则是豆包，哎，主要是它的app用起来确实很舒服啊，最新的模型呢叫豆包C的1.6，和前面几位最大的不同就是它没有开源，不过也是有专门的开源版本啊，当然开源这件事情呢有更好，没有也不要去指责别人。百度这个就很有意思啊。哎就在一年前李彦宏还说开源是一种智商税，但是一年后哎，他就把自家的文心4.5给开源了，相信很多人和哎对话的时候呢都会用到联网搜索，基本各家的app都支持，大部分做的还挺不错。那如果你需要更专业更深度的AI搜索，可以关注一下这些专门做AI搜索的产品。Pl好吧我不知道怎么念，但是pl可能是目前这方面热度最高的，传闻苹果都想收购。那这个工具的话呢，用英文去搜，结果还是很不错的。中文的话呢我感觉就比较一般了，feel这方面呢会好一点，因为它可以自动执行多语言搜索。比如说我用中文提问啊，它就会同时用英文去搜密塔。AI搜索呢会有一些来自文档的结果，并且中英文的信息源都有，所以回答的结果也比较好。呃，我自己还是挺常用的。这三个呢都可以设置信息来源。比如说啊你可以专注于学术相关的内容，也可以做深度研究。就是通过分析你的问题，然后大量搜索，最终给出的结果呢会非常详细。还有一些专门做学术类的A搜索，如果你对这方面有需求的话，也可以去看看。呃，知识库你可能没用过啊，但是你可能有让AI根据文档回答问题哪，而知识库则可以理解为是一堆文档啊，毕竟这是个库嘛。啊仔细一想，AI搜索也是差不多的，网页呢可以算作是文档啊。所以你会发现前面提到的几个AI搜索工具呢都支持知识库，还有这些呢是专门做知识库的，或者说以知识库为卖点的工具。Notebook呢它甚至可以自动生成视频来帮你理解内容。国内的话360有个纳米，对格式知识非常丰富，并且还有个知识广场，可以看到别人分享的知识库，呃，喜欢的话就拿过来为己所用。类似的还有腾讯的艾玛，同样呢有大量别人分享的内容啊，并且作为腾讯的产品呢，可以搜索公众号的内容，算是个优势吧。除此之外还有个flow，它这里的知识库呢感觉质量都挺高的，并且界面很有意思啊，是这种节点式的。另外还有很多笔记或者办公软件啊，飞书、no之类的也都陆续支持了知识库。因此非常推荐大家养成收集资料的习惯，这样就可以很好的构建一个属于自己的知识库了。AI图片这里呢我会分为AI深图和AI修图这两大类。首先是AI深图闭源的开源的都有很多模型啊，并且相比于前面提到的大语言模型这一部分呢，很多人会更乐于本地部署啊，这样就可以实现更复杂或者说更有趣的玩法。那me journey的图片以美学和创意建长，可以生成很多风格化的内容。伊然有一个功能，只需要上传一张人像图片，就可以用这个人脸去生成其他图片，不过并不总是非常像啊的的界面呢比较独特，是这种画布的样式，并且可以生成矢量图，而且有丰富的风格预设。国内的话我觉得综合最强的就是字节的c dream你可能没听说过，但是你大概率用过豆包和极梦的AI声图用的都是它。那作为国产模型啊，就可以用中文输入提示词了，还能生成中文字体，简单做一些海报，那还是很不错的。其他的功能像什么面部参考、姿势参考之类的，哎，也都是应有尽有。此外可图的表现也挺不错的，总体感觉下来呢可图2.1和极梦3.1在大多数场景下都可以说是不相上下。极梦3.1美术上的表现可能会更好些。可图里也有参考深图，参考人物的长相或者是图片风格都可以，但是很多功能呢最新的模型都不支持。好在可图2.1的2K分辨率呢，只要有额度就可以一直用。但是极梦的2K呢对于免费用户来说限免三次啊，但是又但是这个极梦的深图速度会快一点，并且每个月的免费额度也更多啊，所以这两个真的是各有千秋了。开源部分呢1.51L虽然说是很久之前的模型了，但是他们的生态比较完善，有很多微调版本以及各种各样的，再加上对于电脑硬件的要求没那么高，所以还是有很多人在用的。Flux 1 ja呢相比于sd 1.5和sd X L在画面表现上强了不少。那前不久官方还出了一个flux 1 de居然是一个小小的升级版啊，在画面质量上好了很多。而目前热度最高的则是来自阿里的千万image。作为一个国产的开源深图模型，自然也是支持了中文提示词，并且也可以生成中文了，只不过它的模型贼大，对电脑性能要求很高。好在阿里的魔搭上呢可以直接用，并且也有其他开源模型可以选择，这一点还是非常不错的。不过就我个人而言，目前最喜欢的深图模型是2.2，也是阿里的。但它本身呢是一个视频模型啊，不过用来深涂倒是相当的不错，特别是写实的图片啊，细节质感真的很到位啊。顺便一提，如果你现在想在本地玩开源的AI生图，基本上就绕不开外和康复。S 1 web ui相对来说呢会简单一点，最起码是个界面。哎，第一眼还是能看懂大概的，而康U这种节点工作流的界面呢就千变万化了，看着就让人头大对吧？但是如果你现在准备入坑的话呢，还是推荐直接去学康啊。虽然它比较难，但是这才是真正的生产力工具，并且更新的很快，各种新技术都能第一时间用上。在这个快速发展变化的时代啊，这一点是很重要的。生图发展到现在呢已经比较成熟了啊，看着没有什么太大的惊喜，反而是AI修图这一块呢倒是很有意思。目前主要有这么几个模型，那其中seedit很多人其实都用过了，哎，豆包里的AI修图用的就是这个模型，在极梦里也是一样的，涉及到图生图的任务啊都是这个模型，它的表现呢其实还是挺不错的，可以修改汉字啊。当然要说最近的话最火的还得是娜诺布，这个模型的正式名称啊其实是杰2.5 flash image，但是娜诺布这个名称太受欢迎了，它的一致性非常强，并且可以完成很复杂的一些操作。比如说这个手办风格的布，结果呢很明显就是最好的。不过呢它也不是绝对最好，而且对于中文支持呢，诺布也不够好，这方面吉会强很多。开源里呢目前最新的是千问image edit支持改字，但比较可惜的是我感觉它的一致性有一点糟糕。那flax contact呢虽然不支持修改中文，但是一致性表现比较好，会比千问稳定很多。开源模型呢它有个优势啊，就是可以依靠laa在特定任务中获得更好的结果。比如说在修复照片的时候呢，搭配专门训练的laa效果可以说是非常不错了。视频这块首先是基础的文字或者图片去生成模型，目前这几个模型表现都非常不错。谷三呢你大概率刷到过像这种切割物体还有动物自拍的视频呢，很多都是用三做的。See dance也是豆包极梦里的模型啊，你可能也用过。前段时间极梦上线了智能多帧啊，以往的AI视频基本上就是首尾两帧嘛。但是这个智能多帧的一次性上传可以最多支持十张图片啊，用于引导视频的生成。那我这里也是简单测试了一下，借助吉梦和娜诺布拉娜生成了七张图片作为关键帧，然后简单写了点提示词，调整了一下时长啊，主要是这个免费积分就那么多了，最后除了一部分没衔接好之外呢，其他的都很丝滑。如果把提示词写的再详细点，效果应该会更好。可林的2.1版本前段时间更新了首尾帧功能，即便是两张不太相关的图片，也能生成很丝滑的过渡。那这个结果里前面还是很完美的啊，要是后面人走到窗边的时候呢，再自然一点就更完美了。比度Q 1的参考深视频非常有意思，可以一次性参考多张图片里的元素来生成视频啊，非常适合做一些创意内容啊。不得不说这个真的很好玩。海螺有很多agent模板，可以快速复现一些有意思的效果，大大降低了使用门槛。咪谷可以说是专门用来做鬼畜的啊，像这种替换人物的视频你应该有刷到过啊，不说效果多好，但就是很有意思。Field很适合用来做各种特效，比如说前段时间很火的人物花瓣飘散，很多都是用它做的。那mor呢这个交互很有意思啊，可以把图片转为点云，然后你就可以通过拖动来控制视角，最后再生成视频，角度和运镜都可以精准控制啊，可惜人物会严重变形。开源模型这边呢现在最火的毫无疑问就是弯2.2和2.1了。2.2比较新，画面表现比2.1更好。但是2.1生态完善，支持各种玩法呢适配的很好。开源模型呢这边还有一个优势搭配训练好的人物在生成视频的时候一致性的表现会好很多。那我测试下来感觉比前面的那些闭源模型还要强。A编辑视频这方面呢目前主要有这么几个模型支持runway el呢是看下来综合表现最强的，不仅可以实现风格上的转换，局部元素的修改，还可以给一段视频生成不同角度的新视频。未来分辨率画质足够的前提下，很有可能会被用于影视特效的制作，这也会极大程度的降低特效制作的成本和门槛。开源方面基本上就只有阿里的2.1这一个选择。通过康里各种工具的搭配，能实现闭源模型的绝大多数功能，像什么风格转换、动作迁移、局部重绘、口型匹配，这些都没有问题啊，不过可能就是需要自己折腾一下了。谁让知是开源的呢？数字人呢其实也可以算作是一种恩视频，它其实没有听起来那么高大上，这基本可以分为两类嘛，一种是实拍加替换口型，另一种就是整个画面都是AI生成的。那前者呢目前就更为常见一些，你在一些直播间可能就已经刷到过了，哎，只需要稍微注意一下口型还有语音的自然度，就很容易发现了。呃，话又说回来，目前呢做数字人的平台呢其实有很多啊。这里黑井算是做的不错的，各种语言口琴表现都可以，视频清晰度也能很高啊，前提是你得愿意氪金。数字人其实也可以算作是一种A视频这一类的话我感觉基本可以分为两种。一种啊是实拍加替换口型对吧？另一种呢就是这个整个画面呢都是AI生成的。汉源方面，阿里的Y2.2V和基于Y2.1的infinity talk效果不错，其中infinity感觉会更好一点。数字人其实也可以算作是一种视频。这一类的话我感觉基本可以分为两种。一种啊是实拍加AI替换口型对吧？另一种呢就是这个整个画面呢都是AI生成的。那还有一个对口型的开源项目叫黑J现, 在可能是被黑警警告了吧，所以他就改名了AI音频。这里呢目前主要有这么几个分类。首先这个语音合成也叫T S to。这些声音呢你一定有听过的，注意看这个男人叫小帅，这个女人叫小美。小明今天下午就早早的起床了，结果天还没亮，太阳倒是挺大的。他们基本上呢就是来自这几个平台嘛里的这个语音呢，虽然说中文还有点口音，但整体还是非常逼真的。Eleven虽然早期中文口音很重啊，但是最近更新之后可以说是基本没有口音了，哎，表现也很不错。然后门口的猫给我送了外卖，打开一看，昨天的手机忘记充电了。然而我最近听到最真实最自然的这个AI语音呢，其实是豆包里的这个播客，真的很真啊。最早的这个语音合成它是靠什么来实现的？最早的话其实是呃18世纪的末期就有了，那个时候完全是靠机械。Ok另外还有一些工具啊，比如说海螺和eleven呢，已经支持了通过文字描述来生成一个音色，这样就可以做到个性化定制，而不只是局限于某些预设的音色。声音克隆方面，这几个我感觉都挺不错的啊，并且现在的绝大多数呢都不需要训练了，几秒钟的音频就可以直接克隆。而且还很像这方面开源闭源做的都很不错，选择也很多。但其隐私问题的话呢，优先推荐开源模型，可以自行部署啊。当然了在一些细节情感上可能还是商业化的闭源模型表现会更好一些。和声音课文类似的呢是音色转换，哎，直白点说就是变声器。这种所谓的AI歌手就是这么来的。那service和rvc应该算是这个领域里最出名的两个开源项目了。不过这两个呢都需要训练，而C C呢目前已经不需要训练了，只需要通过几秒钟的音频啊，大多数音色都能够被准确的还原。如果说这个开源项目不会本地部署的话呢，可以关注一下我啊，后面会呃做一些相关的教程，呃，希望我不会割了。那在AI音乐这块呢，主要有这么几个，其中so是目前表现最好的本期视频你们听到的几个音乐就是用so生成的。说到这里呢，不知道你们还记不记得沙，对，就是他的音乐也是用生成的。那还有一类是A生成音效。这个前面很多AI视频的工具呢，其实都可以做到V 3算是比较出色的，可以很好的和视频内容匹配上。开源方面，腾讯前段时间有个叫fl的项目，表现挺不错的。总体来看的话呢，这块还在发展阶段，但还是那句话，未来可期啊。那说完这些模型和应用，接下来我们聊聊现在最火的概念之一a agent。如果说前面的那些AI是各种器官，那agent就是把这些器官组合起来的一个智能系统，让AI不再只是简单的被动回答问题，而是能主动的完成各种复杂任务。不过在正式介绍a ent之前，我们得了解一个很重要但你可能没有听说过的东西，是model context o模型上下文协议由在2024年11月发布并开源的一种通用协议。它的出现呢就是为了让大圆模型能够更好的去调用各种各样的工具。在以前呢大语言模型如果想调用一种外部工具或者说是服务，那就得单独开发接口，非常麻烦。而协议呢是开源的，大家都能用，并且设计的不复杂，实践起来比较简单。再加上作为的人工智能公司有很大的影响力，mcp用起来效果也不错，哎，慢慢的大家也都愿意用了。再加上呢那个时候AI agent的热度也是越来越高啊，天时地利人和之下，mcp哎就发展的越来越好了。AI agent也因此可以很方便的调用各种各样的工具了。就平常而言呢，只要你用的那个软件支持的啊，比如说这个开源的chstudio，那就可以让各种模型调用各种的服务。比如说用高德地图查询地点规划路线，或者是用windows p让AI操控电脑，总之玩法非常多样。那现在AI对这个文本、图片、视频、音频都可以处理了，并且也能调用各种工具了。如果说我们把这些都整合到一块，让AI自己去分析问题，制定计划、做出角色、执行任务会怎么样呢？哎，那么恭喜你啊发明了AI agent，哎，没错，AI agent就是这么个东西，一种能够自主感知环境，做出决策并执行的智能系统。中文的话你可以叫它AI智能体代理，呃，或者直接叫A键的话，哎，只要能够这个了解就行了。那目前市面上的A键的还挺多啊，你要说哪个最好用，呃，其实我也不知道，因为这个还是一个早期的探索阶段嘛，比如说这个min很多人应该都听说过啊，毕竟之前还是挺火的。用起来就是给他一个任务，它就会自动规划，一步步执行，最后产出你要的东西，并且可以继续提要求。我让他分析外卖大战，然后做成一个网页，哎，这个结果其实还行吧，基本上可以说是满足要求了。然后呢，我把类似的任务又丢给了其他几个键，基本上都能够按照要求产出内容的。这个布局我个人还是很喜欢的，能够清晰的看到整个流程。可惜最终生成的网页有bug，导致最后一部分内容没法看到。天空的内容呢看起来很不错啊，但是这个网页就太像一份文档了，看着不够精美。扣子空间的网页样式呢还算不错，但是内容比较简陋。杰spk看起来不仅网页设计上呢更加精美，内容上也足够详细。卡罗min你max agent呢网页在设计精美的同时还能做一些交互，内容呢也算挺充足的，只不过呃少了明确的数据来源，但瑕不掩瑜吧，总体来看非常的不错。那至于说最差的就是百度的肩啊，内容不仅少，而且还是非常明显的错误啊。我不是说其他几个数据都是对的，这个我也没有校对过，但是百度的这个一眼就能看出来，不过可能也是我的用法不对吧，我还得再学习学习啊。那这里有一部分检呢用来做的也是很合适啊，比如说扣空间，它本质上呢还是生成一个网页，不过转为的格式导出那是没问题的，但用网页的方式来做的，我觉得还是能够被称为的的。因为这里的排版布局确实是AI通过代码生成的。那其他很多所谓的AI ppt其实就是自动套模板。比如说k米的ppt助手就是这样的，好处就是速度快，并且相对来说会更精美一点，但这个就取决于模板的质量了嘛。那还有个叫a ppt的a ppt产品也是套模板的，不过模板是真的多，但是价格也是真的贵。所以如果你只是偶尔用用呢和扣空间就挺不错的。这些基本都可以说是通用A键的，还有一些特定领域的A键的，比如说专注于图像生成或者视频生成，不用写复杂的提示词就可以生成效果很不错的图片，因为它会有这么一个思考优化的过程。还有一些呢是交互方式上的创新，特别是这种画布啊，创作起来的时候呢会舒服很多。另外还有很多A编程软件也可以算作是一种agent啊，毕竟它也会自动分析任务，然后做出规划，再自动执行，一句话就能生成一个网页或者是程序啊，但还是需要人类去测试，然后一直给反馈，不然可能就会有很多的bug。目前个人认为agent的发展处于比较早期的阶段，归根结底啊还是要依赖于这个未埃模型的发展。一旦模型能力足够强大，那过去很多步骤才能实现的功能可能一步就能解决。还有一些AI呢比较小众或者说太早期了啊，我这里就简单提一嘴啊。首先是用这个AI生成三维模型，发展到现在也是越来越能用了，只不过对很多人来说可能就没啥用，除非你是做三维相关工作的。那另一个也是生成三维模型，不过这个就比较有意思，那就是扫描现实生活中的物品，或者说是建筑啊房间之类的那相比于拍照或者视频，这个就能够更好的还原当时的记忆。这方面目前有一种叫高斯坡溅的技术，效果很不错。有这么几个软件可以实现，大家感兴趣的可以去试试。那影视飓风之前还做过一期高斯坡溅相关的视频，并且那个还是动态的，但是呢离普及还有很大的距离啊，所以咱们就整一个静态的玩玩就好了。那另外还有一种模型叫世界模型。比如说谷的吉米3，看着像是个AI视频，但是这里呢是可以自由操控方向的，就像玩游戏一样。而一切都是一实时生成的，就好像是创建了一个世界一样。那现在基本都是处于一个预览阶段吧，还没有到实际应用，所以咱们可以关注一下啊。希望以上的这些内容能够对你有所帮助啊，也欢迎各位点个关注啊。我后面我也会更新更多软件相关内容，总之点个关注绝对不亏啊。啊，按理来说其实到结尾应该来点升华，对吧？但是这个我还真不太会啊，但是来都来了，那就整两句吧。回顾过去这几年AI的发展，我们见证了一个真正的技术爆发期。从简单的文字聊天到一句话就能生成精美的图片、视频，再到能够自主决策的agent，AI正走向我们生活的每个角落。那最让我感慨的是，技术的门槛越来越低，以前需要专业团队才能完成的工作，现在普通人用也能搞定。这不仅仅是工具的进步，更是一场可以预见的生产力革命。当然，新的技术总是伴随着新的挑战，版权、伦理、就业这些问题依然需要我们认真面对。但无论如何，悲的趋势已经不可阻挡，它不再是遥远的未来，而是当下的现实。与其担心被AI取代，不如尝试学着用好AI啊，比如最后结尾这段内容啊，我就是让卡来帮忙了啊，当然其他部分啊都是我自己写的，纯手工啊。好，那么好了，以上就是本期视频的全部内容了。如果喜欢的话不要忘了点关注，我们下期再见，拜拜。", "status": "success"}, {"index": 12, "link": "https://www.douyin.com/video/7548437874455121162", "timestamp": "2025-09-11T12:18:46.966269", "function": "text", "video_id": "7548437874455121162", "title": "全网最深度解读Optimus机器人扮猪吃老虎事件 Optimus“硅基大脑” 的强大能力\n#Optimus #硅基大脑 #特斯拉 #马斯克 #人工智能", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d30mqnvog65tf5kmn17g&ratio=720p&line=0", "text_content": "你现在看到的opti机器人视频被无数人吐槽反应迟钝，sorry, I don't have real time info走路人那么，opmas斯的表现真的很差吗？真相恰恰相反。这段视频正是在展示opmas硅基大脑的强大能力。下面daily可为大家详细解读。首先我们先聊聊主流机器人为什么不智能。目前大多数机器人采用串行任务分解架构，把机器人的能力拆分为三个部分，交互、移动、操作。他们提前编写好交互规则，机器人在根据指令一步一步执行。问题是这类机器人本质上只是执行命令的电脑规则之外的任务，他们完全无能为力。而在真实场景中，一旦遇到随机事件或者目标变化，这种机器人几乎必然宕机。这段opma斯机器人视频展示了特斯拉在机器人大脑技术领域的重大突破，统一模型的纯视觉端到端系统，机器人不再按部就班串行处理，而是由交互、移动、操作三个模块同时并行处理，彼此交叉验证、同步纠错。这意味着机器人具备了真正的鲁棒性，能，随机处理事件，甚至能临时调整任务目标，这对机器人的实际场景应用至关重要。因此每次和opmas交互时，三个模块会同时工作。视频中看似反应迟钝，原因是需要同时处理海量的数据理解了op的原理，让我们再来解读一下视频，视频中mark的问题是随机提出的，如果是传统机器人系统里没有这个指令，它就无法解读任务，也不会有后续操作。就算接入代源模型，也只是会停留在交互这个环节，无法执行任务，而阿斯的表现则完全不同，他先表示没有实时信息说明语言模型可能是本地部署，并给出可以带mark去厨房找可乐的建议。这里的底层逻辑就是opti交互移动操作三个模块并行工作交叉验证操作模块评估厨房可能有可乐移动模块评估可以抵达交互模块获取足够的信息后提出建议。可以带mark去厨房找一下三个模块同时工作交叉验证提出了一个合理的解决方案。看到这里，你是否发现op对于任务的拆解能力几乎和人类一模一样。特斯拉纯视觉端的端技术让机器人实现了从被动执行任务到主动思考、主动建议、主动行动的里程碑式突破，赋予了机器人真正意义上的归基大脑。即将发布的特斯拉AI 5芯片将比H W 4性能提升十倍，opti机器人的任务处理能力也将获得质的飞跃。关注daily mask，我们会持续更新opti机器人的最新进展。Do not forget to subscribe and daily musk. ", "status": "success"}, {"index": 13, "link": "https://www.douyin.com/video/7546606383500643618", "timestamp": "2025-09-11T12:18:59.226791", "function": "text", "video_id": "7546606383500643618", "title": "打破信息差！一个视频，全面了解AI发展现状！ 本期视频，我整理了目前市面上主流的 AI 模型和应用，带你快速了解 2025 AI 发展现状。当然本期视频是大众化的，所以不会特别深入，目的就是对整体有个大概的了解。\n#ai新星计划  #2025开学季 #AI #人工智能#开学的精选", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?line=0&logo_name=aweme_diversion_search&ratio=720p&video_id=v0200fg10000d2te71nog65ph4fk7kcg", "text_content": "你知道2025年的AI发展到什么水平了吗？本期视频我整理了目前市面上主流的AI模型和应用，带你快速了解2025年AI发展现状啊，当然本期视频是比较大众化的，所以不会特别深入，目的就是对整体有一个大概的了解。那我会把AI呢粗略的分为了这几个方面。那挑尼感兴趣的观看啊，当然了如果嗯你能顺便再点个关注那就更好了。首先就得说说大语言模型，就是我们平常通过文字来聊天对话的AI那之所以叫大语言模型，主要是因为它的参数量真的很多，动不动就几十亿、上百亿、上千亿。那这个大语言模型呢可以说是很多A应用的基础了。而且这一波的AI热呢也是2022年年底啊由OpenAI发布的恰GPT带起来的。当年的GPT 3.5就是个大语言模型，目前这一领域的主要模型有以下这些，那opi肯定不用多说嘛，GPT系列现在依然是顶流，但是今年8月初发布了g5, 虽然也是一个非常厉害的模型啊，但是很多人对此并不满意，反而是更喜欢上一代的g 4欧，不过总体来说opa的模型还是很厉害的，那谷的g系列有很强大的多模态功能啊。也就是说一个模型就可以同时处理文本、图片、视频、音频。那目前最新的是g 2.5 pro啊，如果你想体验的话，可以去g的网页或者是谷stu 6给的免费额度还挺多的啊。比open要大方。而这个是一家由OpenAI前员工创立的公司，也非常厉害。那他家的cloud模型呢在编程以及A键的任务上表现相当出色。目前最新最强的是cloud 4.1，也是现在编程方面最厉害的模型之一了。马斯克的也值得关注，特别是他在app里呢整了一个伴侣啊，听了一下嗯感觉是有点东西的。至于说me塔拉玛之前热度也很高，在open不open的时候meta也算是扛起了开源的大旗，只是最新的拉玛四表现并没有特别亮眼，所以说呃好像也没有什么热度了啊。这里不得不再提一嘴open a它之所以叫open，也是因为它的初衷呢是为了要对抗垄断，保持开放啊，但是后来将渐渐的就不那么open了，g模型从g 3开始就不再开源了啊，直到过了六年啊，在今年的八月初才再次开源了一个gbt s这个模型嗯就不算特别厉害了，不如一些国产的开源大模型。反观国内呢，数量上那是相当的多，开源的也很多。那迪大家都知道了，目前最新的呢是迪V3.1，不要小看这0.1的版本变动啊，这回的V3.1呢变成了一个混合模型。也就是说它既可以像R一那样思考，也可以像V三那样直接回答。所以以后可能就没有R 2了，而会变成V 4、V五这种。那这个V3.1最近还出了一个比较严重的bug，日常使用到还好，但是在工作环境当中就得多多留意了。阿里的千问系列虽然没有像deep stick那样超级爆火锅，但表现一直都挺不错，并且也是一直在开源。目前最新的是千问三系列，有大大小小思考或者不思考多个版本。综合最强的就是千问3235B杠A22B那此外还有个coder版本啊，是专门为了编程优化的，在这方面的表现也是名列前茅了啊，质朴也很喜欢开源，它的名气呢可能不如前两者那么大，但是做的也很早。最新的g 4.5系列呢用着也是非常不错，推荐各位去试试。那不知道是不是受到了别家的影响啊，今年也开源了他们的旗舰模型K 2。要知道它之前可没有开源过旗舰模型。那我个人还是很喜欢k米K 2的，在电脑上就经常用它。哎，不过在手机上用的最多的则是豆包，哎，主要是它的app用起来确实很舒服啊，最新的模型呢叫豆包C的1.6，和前面几位最大的不同就是它没有开源，不过也是有专门的开源版本啊，当然开源这件事情呢有更好，没有也不要去指责别人。百度这个就很有意思啊。哎就在一年前李彦宏还说开源是一种智商税，但是一年后哎，他就把自家的文心4.5给开源了，相信很多人和哎对话的时候呢都会用到联网搜索，基本各家的app都支持，大部分做的还挺不错。那如果你需要更专业更深度的AI搜索，可以关注一下这些专门做AI搜索的产品。Pl好吧我不知道怎么念，但是pl可能是目前这方面热度最高的，传闻苹果都想收购。那这个工具的话呢，用英文去搜，结果还是很不错的。中文的话呢我感觉就比较一般了，feel这方面呢会好一点，因为它可以自动执行多语言搜索。比如说我用中文提问啊，它就会同时用英文去搜密塔。AI搜索呢会有一些来自文档的结果，并且中英文的信息源都有，所以回答的结果也比较好。呃，我自己还是挺常用的。这三个呢都可以设置信息来源。比如说啊你可以专注于学术相关的内容，也可以做深度研究。就是通过分析你的问题，然后大量搜索，最终给出的结果呢会非常详细。还有一些专门做学术类的A搜索，如果你对这方面有需求的话，也可以去看看。呃，知识库你可能没用过啊，但是你可能有让AI根据文档回答问题哪，而知识库则可以理解为是一堆文档啊，毕竟这是个库嘛。啊仔细一想，AI搜索也是差不多的，网页呢可以算作是文档啊。所以你会发现前面提到的几个AI搜索工具呢都支持知识库，还有这些呢是专门做知识库的，或者说以知识库为卖点的工具。Notebook呢它甚至可以自动生成视频来帮你理解内容。国内的话360有个纳米，对格式知识非常丰富，并且还有个知识广场，可以看到别人分享的知识库，呃，喜欢的话就拿过来为己所用。类似的还有腾讯的艾玛，同样呢有大量别人分享的内容啊，并且作为腾讯的产品呢，可以搜索公众号的内容，算是个优势吧。除此之外还有个flow，它这里的知识库呢感觉质量都挺高的，并且界面很有意思啊，是这种节点式的。另外还有很多笔记或者办公软件啊，飞书、no之类的也都陆续支持了知识库。因此非常推荐大家养成收集资料的习惯，这样就可以很好的构建一个属于自己的知识库了。AI图片这里呢我会分为AI深图和AI修图这两大类。首先是AI深图闭源的开源的都有很多模型啊，并且相比于前面提到的大语言模型这一部分呢，很多人会更乐于本地部署啊，这样就可以实现更复杂或者说更有趣的玩法。那me journey的图片以美学和创意建长，可以生成很多风格化的内容。伊然有一个功能，只需要上传一张人像图片，就可以用这个人脸去生成其他图片，不过并不总是非常像啊的的界面呢比较独特，是这种画布的样式，并且可以生成矢量图，而且有丰富的风格预设。国内的话我觉得综合最强的就是字节的c dream你可能没听说过，但是你大概率用过豆包和极梦的AI声图用的都是它。那作为国产模型啊，就可以用中文输入提示词了，还能生成中文字体，简单做一些海报，那还是很不错的。其他的功能像什么面部参考、姿势参考之类的，哎，也都是应有尽有。此外可图的表现也挺不错的，总体感觉下来呢可图2.1和极梦3.1在大多数场景下都可以说是不相上下。极梦3.1美术上的表现可能会更好些。可图里也有参考深图，参考人物的长相或者是图片风格都可以，但是很多功能呢最新的模型都不支持。好在可图2.1的2K分辨率呢，只要有额度就可以一直用。但是极梦的2K呢对于免费用户来说限免三次啊，但是又但是这个极梦的深图速度会快一点，并且每个月的免费额度也更多啊，所以这两个真的是各有千秋了。开源部分呢1.51L虽然说是很久之前的模型了，但是他们的生态比较完善，有很多微调版本以及各种各样的，再加上对于电脑硬件的要求没那么高，所以还是有很多人在用的。Flux 1 ja呢相比于sd 1.5和sd X L在画面表现上强了不少。那前不久官方还出了一个flux 1 de居然是一个小小的升级版啊，在画面质量上好了很多。而目前热度最高的则是来自阿里的千万image。作为一个国产的开源深图模型，自然也是支持了中文提示词，并且也可以生成中文了，只不过它的模型贼大，对电脑性能要求很高。好在阿里的魔搭上呢可以直接用，并且也有其他开源模型可以选择，这一点还是非常不错的。不过就我个人而言，目前最喜欢的深图模型是2.2，也是阿里的。但它本身呢是一个视频模型啊，不过用来深涂倒是相当的不错，特别是写实的图片啊，细节质感真的很到位啊。顺便一提，如果你现在想在本地玩开源的AI生图，基本上就绕不开外和康复。S 1 web ui相对来说呢会简单一点，最起码是个界面。哎，第一眼还是能看懂大概的，而康U这种节点工作流的界面呢就千变万化了，看着就让人头大对吧？但是如果你现在准备入坑的话呢，还是推荐直接去学康啊。虽然它比较难，但是这才是真正的生产力工具，并且更新的很快，各种新技术都能第一时间用上。在这个快速发展变化的时代啊，这一点是很重要的。生图发展到现在呢已经比较成熟了啊，看着没有什么太大的惊喜，反而是AI修图这一块呢倒是很有意思。目前主要有这么几个模型，那其中seedit很多人其实都用过了，哎，豆包里的AI修图用的就是这个模型，在极梦里也是一样的，涉及到图生图的任务啊都是这个模型，它的表现呢其实还是挺不错的，可以修改汉字啊。当然要说最近的话最火的还得是娜诺布，这个模型的正式名称啊其实是杰2.5 flash image，但是娜诺布这个名称太受欢迎了，它的一致性非常强，并且可以完成很复杂的一些操作。比如说这个手办风格的布，结果呢很明显就是最好的。不过呢它也不是绝对最好，而且对于中文支持呢，诺布也不够好，这方面吉会强很多。开源里呢目前最新的是千问image edit支持改字，但比较可惜的是我感觉它的一致性有一点糟糕。那flax contact呢虽然不支持修改中文，但是一致性表现比较好，会比千问稳定很多。开源模型呢它有个优势啊，就是可以依靠laa在特定任务中获得更好的结果。比如说在修复照片的时候呢，搭配专门训练的laa效果可以说是非常不错了。视频这块首先是基础的文字或者图片去生成模型，目前这几个模型表现都非常不错。谷三呢你大概率刷到过像这种切割物体还有动物自拍的视频呢，很多都是用三做的。See dance也是豆包极梦里的模型啊，你可能也用过。前段时间极梦上线了智能多帧啊，以往的AI视频基本上就是首尾两帧嘛。但是这个智能多帧的一次性上传可以最多支持十张图片啊，用于引导视频的生成。那我这里也是简单测试了一下，借助吉梦和娜诺布拉娜生成了七张图片作为关键帧，然后简单写了点提示词，调整了一下时长啊，主要是这个免费积分就那么多了，最后除了一部分没衔接好之外呢，其他的都很丝滑。如果把提示词写的再详细点，效果应该会更好。可林的2.1版本前段时间更新了首尾帧功能，即便是两张不太相关的图片，也能生成很丝滑的过渡。那这个结果里前面还是很完美的啊，要是后面人走到窗边的时候呢，再自然一点就更完美了。比度Q 1的参考深视频非常有意思，可以一次性参考多张图片里的元素来生成视频啊，非常适合做一些创意内容啊。不得不说这个真的很好玩。海螺有很多agent模板，可以快速复现一些有意思的效果，大大降低了使用门槛。咪谷可以说是专门用来做鬼畜的啊，像这种替换人物的视频你应该有刷到过啊，不说效果多好，但就是很有意思。Field很适合用来做各种特效，比如说前段时间很火的人物花瓣飘散，很多都是用它做的。那mor呢这个交互很有意思啊，可以把图片转为点云，然后你就可以通过拖动来控制视角，最后再生成视频，角度和运镜都可以精准控制啊，可惜人物会严重变形。开源模型这边呢现在最火的毫无疑问就是弯2.2和2.1了。2.2比较新，画面表现比2.1更好。但是2.1生态完善，支持各种玩法呢适配的很好。开源模型呢这边还有一个优势搭配训练好的人物在生成视频的时候一致性的表现会好很多。那我测试下来感觉比前面的那些闭源模型还要强。A编辑视频这方面呢目前主要有这么几个模型支持runway el呢是看下来综合表现最强的，不仅可以实现风格上的转换，局部元素的修改，还可以给一段视频生成不同角度的新视频。未来分辨率画质足够的前提下，很有可能会被用于影视特效的制作，这也会极大程度的降低特效制作的成本和门槛。开源方面基本上就只有阿里的2.1这一个选择。通过康里各种工具的搭配，能实现闭源模型的绝大多数功能，像什么风格转换、动作迁移、局部重绘、口型匹配，这些都没有问题啊，不过可能就是需要自己折腾一下了。谁让知是开源的呢？数字人呢其实也可以算作是一种恩视频，它其实没有听起来那么高大上，这基本可以分为两类嘛，一种是实拍加替换口型，另一种就是整个画面都是AI生成的。那前者呢目前就更为常见一些，你在一些直播间可能就已经刷到过了，哎，只需要稍微注意一下口型还有语音的自然度，就很容易发现了。呃，话又说回来，目前呢做数字人的平台呢其实有很多啊。这里黑井算是做的不错的，各种语言口琴表现都可以，视频清晰度也能很高啊，前提是你得愿意氪金。数字人其实也可以算作是一种A视频这一类的话我感觉基本可以分为两种。一种啊是实拍加替换口型对吧？另一种呢就是这个整个画面呢都是AI生成的。汉源方面，阿里的Y2.2V和基于Y2.1的infinity talk效果不错，其中infinity感觉会更好一点。数字人其实也可以算作是一种视频。这一类的话我感觉基本可以分为两种。一种啊是实拍加AI替换口型对吧？另一种呢就是这个整个画面呢都是AI生成的。那还有一个对口型的开源项目叫黑J现, 在可能是被黑警警告了吧，所以他就改名了AI音频。这里呢目前主要有这么几个分类。首先这个语音合成也叫T S to。这些声音呢你一定有听过的，注意看这个男人叫小帅，这个女人叫小美。小明今天下午就早早的起床了，结果天还没亮，太阳倒是挺大的。他们基本上呢就是来自这几个平台嘛里的这个语音呢，虽然说中文还有点口音，但整体还是非常逼真的。Eleven虽然早期中文口音很重啊，但是最近更新之后可以说是基本没有口音了，哎，表现也很不错。然后门口的猫给我送了外卖，打开一看，昨天的手机忘记充电了。然而我最近听到最真实最自然的这个AI语音呢，其实是豆包里的这个播客，真的很真啊。最早的这个语音合成它是靠什么来实现的？最早的话其实是呃18世纪的末期就有了，那个时候完全是靠机械。Ok另外还有一些工具啊，比如说海螺和eleven呢，已经支持了通过文字描述来生成一个音色，这样就可以做到个性化定制，而不只是局限于某些预设的音色。声音克隆方面，这几个我感觉都挺不错的啊，并且现在的绝大多数呢都不需要训练了，几秒钟的音频就可以直接克隆。而且还很像这方面开源闭源做的都很不错，选择也很多。但其隐私问题的话呢，优先推荐开源模型，可以自行部署啊。当然了在一些细节情感上可能还是商业化的闭源模型表现会更好一些。和声音课文类似的呢是音色转换，哎，直白点说就是变声器。这种所谓的AI歌手就是这么来的。那service和rvc应该算是这个领域里最出名的两个开源项目了。不过这两个呢都需要训练，而C C呢目前已经不需要训练了，只需要通过几秒钟的音频啊，大多数音色都能够被准确的还原。如果说这个开源项目不会本地部署的话呢，可以关注一下我啊，后面会呃做一些相关的教程，呃，希望我不会割了。那在AI音乐这块呢，主要有这么几个，其中so是目前表现最好的本期视频你们听到的几个音乐就是用so生成的。说到这里呢，不知道你们还记不记得沙，对，就是他的音乐也是用生成的。那还有一类是A生成音效。这个前面很多AI视频的工具呢，其实都可以做到V 3算是比较出色的，可以很好的和视频内容匹配上。开源方面，腾讯前段时间有个叫fl的项目，表现挺不错的。总体来看的话呢，这块还在发展阶段，但还是那句话，未来可期啊。那说完这些模型和应用，接下来我们聊聊现在最火的概念之一a agent。如果说前面的那些AI是各种器官，那agent就是把这些器官组合起来的一个智能系统，让AI不再只是简单的被动回答问题，而是能主动的完成各种复杂任务。不过在正式介绍a ent之前，我们得了解一个很重要但你可能没有听说过的东西，是model context o模型上下文协议由在2024年11月发布并开源的一种通用协议。它的出现呢就是为了让大圆模型能够更好的去调用各种各样的工具。在以前呢大语言模型如果想调用一种外部工具或者说是服务，那就得单独开发接口，非常麻烦。而协议呢是开源的，大家都能用，并且设计的不复杂，实践起来比较简单。再加上作为的人工智能公司有很大的影响力，mcp用起来效果也不错，哎，慢慢的大家也都愿意用了。再加上呢那个时候AI agent的热度也是越来越高啊，天时地利人和之下，mcp哎就发展的越来越好了。AI agent也因此可以很方便的调用各种各样的工具了。就平常而言呢，只要你用的那个软件支持的啊，比如说这个开源的chstudio，那就可以让各种模型调用各种的服务。比如说用高德地图查询地点规划路线，或者是用windows p让AI操控电脑，总之玩法非常多样。那现在AI对这个文本、图片、视频、音频都可以处理了，并且也能调用各种工具了。如果说我们把这些都整合到一块，让AI自己去分析问题，制定计划、做出角色、执行任务会怎么样呢？哎，那么恭喜你啊发明了AI agent，哎，没错，AI agent就是这么个东西，一种能够自主感知环境，做出决策并执行的智能系统。中文的话你可以叫它AI智能体代理，呃，或者直接叫A键的话，哎，只要能够这个了解就行了。那目前市面上的A键的还挺多啊，你要说哪个最好用，呃，其实我也不知道，因为这个还是一个早期的探索阶段嘛，比如说这个min很多人应该都听说过啊，毕竟之前还是挺火的。用起来就是给他一个任务，它就会自动规划，一步步执行，最后产出你要的东西，并且可以继续提要求。我让他分析外卖大战，然后做成一个网页，哎，这个结果其实还行吧，基本上可以说是满足要求了。然后呢，我把类似的任务又丢给了其他几个键，基本上都能够按照要求产出内容的。这个布局我个人还是很喜欢的，能够清晰的看到整个流程。可惜最终生成的网页有bug，导致最后一部分内容没法看到。天空的内容呢看起来很不错啊，但是这个网页就太像一份文档了，看着不够精美。扣子空间的网页样式呢还算不错，但是内容比较简陋。杰spk看起来不仅网页设计上呢更加精美，内容上也足够详细。卡罗min你max agent呢网页在设计精美的同时还能做一些交互，内容呢也算挺充足的，只不过呃少了明确的数据来源，但瑕不掩瑜吧，总体来看非常的不错。那至于说最差的就是百度的肩啊，内容不仅少，而且还是非常明显的错误啊。我不是说其他几个数据都是对的，这个我也没有校对过，但是百度的这个一眼就能看出来，不过可能也是我的用法不对吧，我还得再学习学习啊。那这里有一部分检呢用来做的也是很合适啊，比如说扣空间，它本质上呢还是生成一个网页，不过转为的格式导出那是没问题的，但用网页的方式来做的，我觉得还是能够被称为的的。因为这里的排版布局确实是AI通过代码生成的。那其他很多所谓的AI ppt其实就是自动套模板。比如说k米的ppt助手就是这样的，好处就是速度快，并且相对来说会更精美一点，但这个就取决于模板的质量了嘛。那还有个叫a ppt的a ppt产品也是套模板的，不过模板是真的多，但是价格也是真的贵。所以如果你只是偶尔用用呢和扣空间就挺不错的。这些基本都可以说是通用A键的，还有一些特定领域的A键的，比如说专注于图像生成或者视频生成，不用写复杂的提示词就可以生成效果很不错的图片，因为它会有这么一个思考优化的过程。还有一些呢是交互方式上的创新，特别是这种画布啊，创作起来的时候呢会舒服很多。另外还有很多A编程软件也可以算作是一种agent啊，毕竟它也会自动分析任务，然后做出规划，再自动执行，一句话就能生成一个网页或者是程序啊，但还是需要人类去测试，然后一直给反馈，不然可能就会有很多的bug。目前个人认为agent的发展处于比较早期的阶段，归根结底啊还是要依赖于这个未埃模型的发展。一旦模型能力足够强大，那过去很多步骤才能实现的功能可能一步就能解决。还有一些AI呢比较小众或者说太早期了啊，我这里就简单提一嘴啊。首先是用这个AI生成三维模型，发展到现在也是越来越能用了，只不过对很多人来说可能就没啥用，除非你是做三维相关工作的。那另一个也是生成三维模型，不过这个就比较有意思，那就是扫描现实生活中的物品，或者说是建筑啊房间之类的那相比于拍照或者视频，这个就能够更好的还原当时的记忆。这方面目前有一种叫高斯坡溅的技术，效果很不错。有这么几个软件可以实现，大家感兴趣的可以去试试。那影视飓风之前还做过一期高斯坡溅相关的视频，并且那个还是动态的，但是呢离普及还有很大的距离啊，所以咱们就整一个静态的玩玩就好了。那另外还有一种模型叫世界模型。比如说谷的吉米3，看着像是个AI视频，但是这里呢是可以自由操控方向的，就像玩游戏一样。而一切都是一实时生成的，就好像是创建了一个世界一样。那现在基本都是处于一个预览阶段吧，还没有到实际应用，所以咱们可以关注一下啊。希望以上的这些内容能够对你有所帮助啊，也欢迎各位点个关注啊。我后面我也会更新更多软件相关内容，总之点个关注绝对不亏啊。啊，按理来说其实到结尾应该来点升华，对吧？但是这个我还真不太会啊，但是来都来了，那就整两句吧。回顾过去这几年AI的发展，我们见证了一个真正的技术爆发期。从简单的文字聊天到一句话就能生成精美的图片、视频，再到能够自主决策的agent，AI正走向我们生活的每个角落。那最让我感慨的是，技术的门槛越来越低，以前需要专业团队才能完成的工作，现在普通人用也能搞定。这不仅仅是工具的进步，更是一场可以预见的生产力革命。当然，新的技术总是伴随着新的挑战，版权、伦理、就业这些问题依然需要我们认真面对。但无论如何，悲的趋势已经不可阻挡，它不再是遥远的未来，而是当下的现实。与其担心被AI取代，不如尝试学着用好AI啊，比如最后结尾这段内容啊，我就是让卡来帮忙了啊，当然其他部分啊都是我自己写的，纯手工啊。好，那么好了，以上就是本期视频的全部内容了。如果喜欢的话不要忘了点关注，我们下期再见，拜拜。", "status": "success"}, {"index": 14, "link": "https://www.douyin.com/video/7548358290212769034", "timestamp": "2025-09-11T12:20:00.925280", "function": "text", "video_id": "7548358290212769034", "title": "18个月拿到200万用户的小场景SaaS 拆解一个小而美的AI黑马，18个月拿到200万用户，获1000万美金融资。这个项目叫Julius，他找的这个小场景是“数据可视化”，他能用AI将“Excel”变成“各种各样的图表”。#ai  #Agent  #ai创新  #ai案例  #ai应用", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0300fg10000d30i8nvog65r58ielrfg&ratio=720p&line=0", "text_content": "这个人花一周时间做了个AI迅速拿到了Y 4的投资。用18个月在一个小场景拿到了200万用户，刚获得了1000万美金的融资。这个项目叫jai，他找的这个小场景是数据可视化，他能用AI将excel变成各种各样的图表。他们发现全球10亿excel用户里面，绝大部分人都有汇报需求，而传统工作流程不仅效率低、成本高，而且还不够灵活。于是他们就做了这个AI数据分析师，他是怎么做的呢？他模拟人类分析师打造了一套多镜的工作流，先用来驱动数据的预处理，然后用开始写代码生成各种各样精美的图表，最后结合数据指标图表和关键结论生成一个完整的数据分析报告。为了提高准确度，他们用不同的模型来执行不同的任务。比如处理数据用代码用就这样一群每天拼的工作，他们每天生成260万行代码，累计生成了800万份可视化的报告。你看的价值不在于工具的变革，而在于流程的颠覆。技术革命的本质是交互方式的革命，更是执行效率的代差。当一个低效的工作流被彻底重构时，新的物种就会诞生，这就是创新的新机会。换个视角看。", "status": "success"}, {"index": 15, "link": "https://www.douyin.com/video/7548620236992662826", "timestamp": "2025-09-11T12:20:13.248441", "function": "text", "video_id": "7548620236992662826", "title": "AI革命：推理时代来临，赢者通吃！ #ai #推理 #ai新星计划 #waytoagi", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d3119qnog65k7m414c1g&ratio=720p&line=0", "text_content": "AI革命推理时代的来临，赢者通吃。现在世界正在以惊人的速度改变，AI将主导一切从知识经济到物理世界，未来将颠覆所有的领域。那么咱们今天这个短视频的核心观点是什么呢？就是赢者通吃，仅少数的巨头如特斯拉、英伟达和x AI可能会占据着80%的市场份额，总市场高达数十万亿美元。推理比训练重要千倍，边缘计算将引爆A革命？那么给我的感觉啊，A I非常有可能正如同马斯克所说的那样，明年就会到来。这意味着无限的富饶并非遥不可及，而是如影随形的。就是什么呢？99%的失业风险。在这场大的转型之中，伊隆马斯克的X I和擎天柱的原型机器人是关键。而能源是限制性的因素，是唯一可以限制AI指数发展的那堵墙。但能源人类其实并不陌生，只要有需求，能源的供给就是一个时间的问题。所以啊未来的5到10年，世界将剧烈的转型。那么咱们今天就用这个短视频来一起探究一下这场巨变之中都会发生些什么吧。一赢者通吃的AI时代从2019年起啊，仅十家科技巨头，比如说谷歌、亚马逊、苹果、特斯拉和英伟达，他们就贡献了将近60%的股市的涨幅。那么我认为啊未来的这种集中的趋势将更加的极端，三大巨头可能会驱动80%的市场增长，为什么呢？因为AI是自我进化的智能，一旦突破了资源限制，其上限难以预测，领先者将碾压一切。AI的轨迹是一条陡峭的、上升的曲线，也就是说，它已经超越了人类在棋类、围棋和星际争霸这样游戏中的表现。五年前，有谁可以想象，在2025年我们还在争论，我们在争论啊AI是否能够达到A I呢？现在它已经优于多数的医生的诊断，而人类还只是盯着AI，数不清strawberry中的R到底有几个这样的事情，这就是A的遗忘症，也就是说，我们低估了A I的飞速进步。知识工作，比如说编程和数学正在被迅速的取代。几年前啊父母还催孩子去学计算机科学，而如今computer science的毕业生刚毕业就已经发现A已经掌握了一切。二总可总可寻指市场的巨大规模与比特币或者是货币市场相比啊，AI的T M也就是总可寻址市场更为庞大。特斯拉的目标市场包括人形机器人来取代全球最大的支出什么呢？人力的薪金也就是工资啊，那么财富管理只是剩余劳动剩余的一个子集。而AI却直击这个价值创造的源头，知识经济已经被A所吞噬，他已经摄取了所有的百科全书、文章和视频，它的气势汹汹，根本势不可挡。那么接下来是什么呢？就是物理经济，或者咱们称之为巨身AI自动驾驶汽车机器人将改变说现在的这个世界。那么摩根斯坦利的阿达Jonas斯就预言说，AI将消灭包括在他自己在内的大多数分析师的工作。AI的年复合的增长率非常的惊人。数学作为宇宙的语言，被AI通过强化学习快速掌握，他不眠不休的尝试着百万种可能验证简单哎这个奖励啊，呃这样的导向能力远超人类。三苦涩的教训与计算力的竞赛。苦涩的教训啊也就是the bitlesson是由啊Richard Sutton他所提出来的。核心的要点就是简单的算法加海量的数据，再加强大的算力，就等于超人的表现。以阿尔法go为例，早期啊是靠着人类的数据去训练，那么仅能够达到人类的水平，那么通过自我博弈来探索无限的可能，最终他发现了人类根本没有见过的策略。而伊隆马斯克的巨象一号已经超越竞争对手，也就是这个算力的集群啊，而巨象二号会使得差距变得越来越大。马斯克产业它的产业占据着近半的算力，这还不包括700万到800万辆即将具备推理能力的特斯拉的汽车。A竞赛根本没有第二名不赢技术关键的要素就是算力数据哎算法数据和算力。我以为啊我觉得啊马斯克以使命感，也就是他的火星计划A汇集全人类，真的会吸引真正的顶尖人才。X I从麦塔挖走了14名工程师，就组成了精锐的海豹六队，远胜于麦塔的步兵营。马斯克曾经资助demand，创立了OpenAI来对冲谷歌，却意外的加速了。如今啊X又在对冲open，这堪称是戏剧性的对冲啊，也真的是一场跌宕起伏的AI大戏。四推理革命谦备于训练黄仁勋啊，他预测推理的token生成量一年之内会飙升十倍，未来将千倍于训练。训练打造了一个合成大脑，但推理让他执行有用的任务，云推理如拆g边缘，这种推理嵌入到现实之中。割草的机器人、自动驾驶汽车都需要本地的芯片，那么特斯拉的A 6芯片高效低功耗边缘推理啊，利用闲置的车辆进行计算，或许或许啊能够缓解能源的瓶颈。1000万辆特斯拉的车可以进行分布式的负载，抵消能源的限制因素。杰文斯的悖论就提醒了咱们，效率的提升反而会增加总的需求量，会催生出更为强大的创新的潜力。X I的四已经是十倍强化学习后的这种训练了，那么在Arai的基准测试之中就超越了open。而后面的grou p 5会借助具象二号这算力集群或80%的算力都会投入到强化学习之中，那么结果会不言而喻，如同马斯克所预测的那样，会有不小的机会达到AGI5擎天柱机器人万亿的机会。黄仁勋又预测2026年人形机器人将实现高的量产，行业规模会达到很多，那么在2030年会达到50万亿，那么赢家或许能够达到10至20万亿。那么马斯克称擎天柱是史上规模最大的产品，十倍于I phone，也就是现在2万亿的这样的市值。啊，想象一下家政机器人律集律师、厨师、洗衣工于一体，成本啊低于1万美元一台啊，人均两台。当然了，我指的这个是后面的事情，可能是2035年到2040年才会发生，一开始还是会贵一些的啊。那么总时长会是惊人的，也就是多机器人服务于人类。那么他们是不眠不休，一个充电一个工作，这样就会创造无限的价值，真的是难以估量。而这些所有的基础是制造的能力，而这方面我个人觉得中国和特斯拉都有着制造能力最为优秀的基础。6 agi即将来临，市场啊这个预测市场显示有25%的人啊认为2026年能够实现A G I，40%的人认为是2027年之前，那么我觉得啊争论本身表明A I就已经很近了，A在多个领域超人强化学习，打破了人类数据的局限，递归自改进都是agi邻近的脚步声，这脚步声现在是越来越近了，您听到了没有呢？OpenAI I从G边T4O的零强化学习到O 3的大量的强化学习，而X I投入到强化学习上的算力更多。伊隆马斯克他预测明年的将发现新的物理或者科学。有一种结果我很难想象，就是agi可能会取代大多数的职业。2035年前人形机器人会消灭体力劳动，导致90%甚至更高的人类事业。哎，我觉得啊大家都说90%的事业啊，还剩了10%，非常有可能啊这个比例会更高啊，而不会更低。这将是工业革命以来最大的社会变革。7 master plan 4，也就是大师计划的第四部分，可持续的富饶。一龙的第四计划就指向了无限的g dp也就是能源加运输加机器人robotaxi啊哎在年底可能就没有安全源了，他的估值可能会达到7至10万亿，而擎天柱可能会达到20至30万亿。那么富饶的愿景就是机器人劳作，人类来追求生命的意义，但如何进行普惠呢？仍然是无解的社会趋向社会主义或者咱们叫做ubi我个人觉得ubi就是社会主义啊，那么就会有超50%的这种依赖的福利。那么资本主义该走向何方呢？我认为啊资本主义是人类发展史上的奇迹，但贫富的分化是绝对需要管理的，我认为啊应对之道就是ubi而且ubi也会成为一个社会悲化最为重要的指标。只有人类肯一个一个的心甘情愿的下岗，那么我个人觉得悲才能上岗，那么没有ub I怎么可能让我心甘情愿的下岗呢？我个人觉得这是一个基本的逻辑。也就是我再深一步讲啊，就是最早实现或者最优实现ubi的国家，他的AI一定会发展的最好的。因为AI它的本质就是智力智力的本质，那么AI的本质就是要替代人类，那你怎么能够让人类饿着肚子呢，对吧？所以一定要有U鼻I只有这样这个社会才能大规模的推进AI这是一个最基本的逻辑。咱们最后啊再总结一下吧，AI哪里有泡沫？人类200万年之中啊，现在未来的15年是人类历史之中最为灿烂的15年。这才刚刚开始，一直到2040年。大家拭目以待，好好活着，一起来见证历史，来见证这部历史大片之中的高光时刻。", "status": "success"}, {"index": 16, "link": "https://www.douyin.com/video/7545809248664505641", "timestamp": "2025-09-11T12:23:14.763402", "function": "text", "video_id": "7545809248664505641", "title": "3分钟速通10篇AI顶级论文 #AI新星计划  #2025开学季 #AI #Transformer #VIT #开学的精选", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0300fg10000d2s1eafog65s3t93chjg&ratio=720p&line=0", "text_content": "从猫狗不分到10变世间万物，从数学白痴到登上诺贝尔领奖台，在过去的黄金十年，AI是怎么一步步封神的？接下来就请和我速通十篇AI顶级论文，一起解锁这个荒怪陆离的A世界。Alx. Net作为大规模图像分类神经网络，在2012年的image。Net竞赛场用八层卷积神经网络卷死所有对手，并以15.3%的top five错误率遥遥领先alx。Net 2座。伊利亚后来成为了OpenAI的灵魂人物。三座H特老爷子拿了诺贝尔物理奖，而一座Alex则是事了佛一去，在开创深度学习的新纪元后，就消失在了人群中。当大家齐刷沙的跑去追求更深的网络时，却发现一旦网络层数太多，就会撞到梯度消失的屏障，网络堆得越深越容易退化。为了解决这个问题，出身广州的哎圈大神何凯明出手还给神经网络拉了一个巧妙的连接，让低层数的信息可以超近路传递给高层数网络，这条近路呢也叫做残插连接。凭借这个设计，renet跌到了最高1000层。不仅在2015年image。Net上拿下冠军，在其他赛道也全部斩获第一名，残渣连接影响深远，在后世的巨型模型中无处不在，可以说正是renet打开了的潘多拉魔盒，除了视觉任务哎，还需要处理一个字一个字输入的序列问题。最经典的场景就是把一句话从一种语言翻译成另外一种语言。在这个问题上，诞生在深度学习爆发前夜的lstm，妥妥是当仁不让的大哥。他对循环神经网络进行了天才般的改造，通过精巧的门控设计，网络可以记住序列中的关键信息，又能遗忘无关细节。从自然语言处理到语音识别，lstm称霸20年，但一个个排队处理的串行机制让它无法跟上并行计算的时代洪流。于是，当能并行处理序列数据的史诗级模型登场时，lst变成了时代的眼泪。这个史诗级模型想必大家也都猜到了，正是大名鼎鼎的transformer，它的论文啊是少有的能火出圈的学术论文，各种all you need更是被玩出了梗，transformer推翻了lstm那套排队处理的旧秩序。开创了全新的自注意力机制，这个机制可以并行的计算一句话里面任意两个单词的关联程度，这种一步到位的全局视野让模型拥有了超强的长文理解能力。Op在看到了transformer的潜力之后，决定all in自注意力机制创造了当时举世无双的GPT三大模型。而且在几乎穷尽了全世界的血练数据之后，GPT似乎涌现出来人类独有的智慧。追求大一统似乎是人类的天性，在看到transformer处理文本的潜力之后，有好事者就想了，我们可以用这个自然语言模型来分析图片吗，于是飞transformer诞生了。它的方法简单又粗暴，把一张图片切成一个个16乘16的小方块，如果把每一个小方块当做一个句子的单词，图片不就变成了可以直接交给transformer分析的句子吗？结果很惊艳，这个来自语言界的跨界选手在图像识别的战场上取得了苏塔级的战绩，直接挑战卷积神经网络在视觉领域的统治地位。菲特的成功拉开了多模态的序幕，但AI的野心早已不满足于理解，他还想要打造一个虚拟的新世界。想看AI的终极进化吗？点个关注，我们下期见。", "status": "success"}, {"index": 17, "link": "https://www.douyin.com/video/7548381099513089343", "timestamp": "2025-09-11T12:23:26.983660", "function": "text", "video_id": "7548381099513089343", "title": "马斯克擎爆料擎天柱2万美元，AI芯片性能狂飙40倍，掀桌子？ 马斯克擎爆猛料擎天柱2万美元，AI芯片性能狂飙40倍，掀桌子？#机器人 #马斯克 All-In 峰会爆料 #擎天柱 Optimus v3 #人形机器人新进展 #Optimus 技术难点", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?line=0&logo_name=aweme_diversion_search&ratio=720p&video_id=v0200fg10000d30jp1fog65m8dsl55qg", "text_content": "刚刚马斯克在奥运峰会上的最新爆料，简直是要掀翻机器人行业的桌子，内容全是干货，从产品设计到成本再到技术难点，咱们逐一拆解。首先说擎天柱V 3，马斯克明确表示正敲定它的最终设计，还直言这会是个非常了不起的机器人。它的优势集中在三点，一是手动灵活，性能媲美人类，意味着会有一只极其复杂的手，二是搭载能导航并理解现实的AI大脑，三是未来可实现大规模量产。提到这只手，马斯克重点强调了它的重要性。他说，人手是进化出的超精密机器，挥棒球棍、穿针引线、弹钢琴、拉小提琴、组装汽车，全靠这双手。从专业角度看，人手有27至28个自由度控制手部动作的肌肉多在前臂，手本身更像木偶，人类击剑的进化程度堪称惊艳。也正因如此，马斯克坦言，要造通用人形机器人，必须先解决手部难题。目前，特斯拉团队的核心困境就在硬件设计，尤其是手和前臂，这两部分占了整个机器人工程难度的大头。除了手，擎天柱还有个绕不开的坎，供应链。马斯克坦言，人形机器人没有现成供应链，一切都得从零开始造，需要大量垂直整合。不过他也补充，擎天柱比特斯拉以往任何产品都难，但没难到超过新建。逐渐这项目技术门槛之高，大家最关心的成本和价格，马斯克也给了明确预期，一旦年产量达100万台，边际生产成本约2万美元，最多不超2.5万美元。成本主要看两部分，一是AI芯片，单块可能要5000到6000美元甚至更高。二是执行器效率擎天柱每条手臂有26个执行器，含电机、变速箱和电力电子设备，必须提升这部分效率才能压低成本。至于最终售价，马斯克表示由需求决定，后续或根据市场反馈调整。最后，聊到擎天柱大脑的核心AI芯片，马斯克透露，新一代AI五推理芯片是狠角色，部分指标比上一代AI四强40倍。马斯克还暗讽同行其他机器人公司都没搞定这三件难事，也就是复杂手部设计、能理解现实的AI大脑、大规模量产能力。他甚至放预言若擎天柱能成会是有史以来最大的产品。这人形机器人能否如期落地？会不会改变行业？关注苏大，了解每日全球头牌A I。", "status": "success"}, {"index": 18, "link": "https://www.douyin.com/video/7548291554159480115", "timestamp": "2025-09-11T12:23:39.418938", "function": "text", "video_id": "7548291554159480115", "title": "从图灵机到ChatGPT，人工智能是什么时候诞生的？ #AI硬骨头在抖音啃上了#2025开学季#AI新星计划#开学的精选", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d30ehb7og65n0l71og3g&ratio=720p&line=0", "text_content": "这是你大脑里的神经元。如果你在思考神经元就会像这样连接在一起。那机器是如何像人类一样思考的呢？今天开启我们开学季特别节目，开启A的第一课，人工智能的诞生。A的起点啊是从一个最简单的设问开始的。机器能不能像人类一样思考？20世纪40年代呢艾伦图灵迈出了人工智能的第一步。图灵没有直接回答上面的问题，而是提出了一个构想，图灵机为了让大家更好的理解，我们从资料当中推演建了一个模型来给大家做演示。图灵机呢是由一个无限长的纸袋，一个读写头，一套规则板组成的，然后呢通过读改写来完成计算。如果我们要用图灵机来完成3加2等于5的这个计算，图灵机呢便开始启动了读写头会识别纸袋上的信息，然后呢再对应规则板上的规则来进行改写。我们看到现在读到了零图灵记，在规则板上找到了对应的规则之后，选择继续还是改写，最终完成。计算看起来是枯燥无奇的，但是就是这样一个简单的工具被证明可以模拟出所有的计算逻辑。到现在我们日常所用的电脑、手机本质上就是这个图灵机。至此图灵机为冰冷的机械带上了思考的王冠，让有限规则得以孕育无限可能的星辰。图灵开始思考，如果机器能够像人类一样处理信息，它们是否可以诞生意识呢？与此同时，科学家沃尔特皮茨和沃伦麦卡洛克试图从人类大脑的角度来解答机器是否能够思考的问题。1943年，他们两个在稿纸上画出了一个二进制的神经元的时候，那就代表着人类创造出了可以模仿突触放电的数学模型。它让科学家们意识到，人类引以为傲的创造力或许只是宇宙预设的递归函数。这就给人工智能这颗种子浇下了第一滴水。然而种子要发芽，那就无法离开土壤。克劳德香农这位信息论之父给人工智能提供了最肥沃的土壤。Hello here. 15 years something emerfrom, the oratwhich is not from the roscience. 他在1948年提出了信息论，发明了比特这个单位，并且设计了如何用二进制编码来表达和传输信息。相农认为，任何数据，不管是文字、声音还是图像，都可以被转化为二进制形式进行处理。Shsaw binary digmental element communication information could be iled to sequof cooded at the other messacould, then be transmitted over distances with virtually no loss in quality. 这种信息处理方法让机器可以像人类一样处理信息。图灵最初的设问此刻有了答案，接下来呢图灵提出的一个测试，让人工智能这颗种子开始发芽了。1950年他提出了图灵测试。如果说一个人没有办法通过与机器的对话分辨出对方是人还是个机器，那么这台机器就具备了智能。这个测试给后来的科学家们提出了明确的研究方向，人工智能的最终目的，如何让机器能够进行自主思考和推理？1956年，在达特摩斯会议上，约翰麦卡锡等人在图灵测试的基础之上首次提出人工智能不仅可以模拟人类行为，他还能够思考、推理、学习。A悲从最开始的设问正式进入到了科学的领域，人工智能成为了一门独立的学科。从图灵测试到阿尔法狗，再到GPT系列，人工智能已经从一个遥远的幻想变成了现代科技的基石。它从一颗种子历经寒冬积累养分，最终长成了一棵改变世界的参天大树。但是呢我们说这棵大树也并非是无懈可击的，它仍然还藏着许多未解的秘密，也伴随着偏见甚至是恐。A data to public there going to be diinformation. 对于许多人来说，AI还是一个遥远而又神秘的黑盒。而我们制作这个系列的初衷就是为了能够消除信息差，让复杂的AI变得不再高高在上。人工智能不是未来的敌人，这一切也只是开始。Chat gbt问世的两年来，又给我们的生活带来了哪些改变？人工智能这颗种子未来又会怎样开枝散叶赋能各行各业呢？Chat gbt的故事，我们下期再见。", "status": "success"}, {"index": 19, "link": "https://www.douyin.com/video/7547361397613546786", "timestamp": "2025-09-11T12:24:00.118917", "function": "text", "video_id": "7547361397613546786", "title": "国产Banana来了 即梦4.0真香，人物一致性绝了#AI新星计划  #即梦ai #nanobanana #ai修图 #ai测评", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?line=0&logo_name=aweme_diversion_search&ratio=720p&video_id=v0300fg10000d2upn8vog65kn21jqa70", "text_content": "把头发换成绿色的不错，换成长头发还是短的吧。带上鼻环，一只手拿啤酒，一只手拿雪茄，有内味儿。换成西海岸风格的衣服，把背景换成路边摊，漂亮马斯克的退休生活，男人的快乐果然总是那么的朴实无华。而这些都是我用极梦图片4.0模型做出来的好玩效果，这条视频咱们就一起看看它都更新了哪些实用的功能。我们打开极梦选择图片生成，在下拉列表里选择图片4.0的模型比例自己定。先给了一张图，画面中是一男一女的街拍照，让他把景别改成远景，不错，一点没走样。将景别改成女人特写，再改成男人特写，让他们俩调换位置，牛逼，将视角改为俯视，改成仰视，再来个测试。不管我怎么折腾，这里面的人物长相、发型、穿着、色调、场景都能保持统一一致。角色图片一致性的试剂难题这回终于解决了。有了这个功能，拍电影这回是不是觉得又行了？于是我又找了张图，这回是三个人的，我让A随便给我写个故事脚本，让他模仿经典影视作品画面，给出不同的镜头视角、构图、人物动作状态、色调等等，给出中文题示词。我们的目的是为了测试镜头画面，所以故事好不好，合不合理就没有过多的强调。我们看整体的色调保持的非常好，始终是这种冷灰色调的风格，人物一致性保持的也很到位，不管是长相还是穿着，这三个人始终没有出现互相污染的情况。尤其是这个特写镜头，皮肤的纹理质感、图片的清晰度基本上跟拍出来的没什么区别了。注意看原图，其实并没有多么高清的分辨率，而极梦4.0能够通过重新计算来还原近景的细节，并且还能保持不变样，这个真是太牛了。它对提示词的理解力也相当准确。低角度仰拍、三人逆光进入仓库、身影拉长，这几个点全都给做到了。这种潜景时背景模糊的大光圈效果。对比之前的3.0模型来讲，提高了不止一个档次，除了镜头表现力外，功能上也做了很大提升。保持图2的视角，模仿图一的动作，这个理解真是太到位了，等于说同时完成了角度和姿势两步操作。让他把这个人的面罩摘掉，还挺帅的。把头发改成金色，衣服换成橙色，完美。让图一的人穿上图二的鞋，这鞋给的也够准确的，识别物品的能力跟人物一样到位。让图一的角色穿上图二的毛衣和图三的牛仔裤，确实都给穿上了。不过提示词要注意强调一下，毛衣是无领的，裤子是宽松的，否则它容易识别成高领毛衣和直筒牛仔裤。把背景换成森林，这个挺有意境的，感觉像是被音乐带入的一样。去掉脸上的雀斑，再换成长发，嗯，这个比之前好看多了让小丑变成大胖子，哎呀，妈这够辣眼睛的这小丑还是小丑，只是吃的有点多。你甚至可以给他限定区域。比如我上传的这个模特图，点开左下角有这么一个选项，可以用矩形框框选区域，也可以用画笔把你想要的区域涂抹出来。就比如我想让它在这儿背个包，画完点确定保存，再上传个包的款式提示词，你就写在图一蓝色涂抹区域，增加图2的斜挎包，然后他就给你背好了，是不是产品展示图直接就给你解决了。我再给他一幅梵高的自画像，让他复原油画中的人物长相真实风格，身穿棕色呢子大衣，背景是欧洲小镇街景。看看这个效果到位不？在我的印象中，梵高就应该长这样，我再让他替换掉他自己作品中的人物，用图一的画风、动作、表情、动势保持图二的人物长相。好了，现在她又回去了。除了这些玩法外，功能实在太多了。比如给黑白图上色，黑豹变成花豹或者是老虎，再给这个小孩加上火焰特效。只要你的想法够丰富，基本上想要什么效果就都能实现。还想做点什么？把问题打在评论区，咱们下次接着聊。", "status": "success"}, {"index": 20, "link": "https://www.douyin.com/video/7547590859831790898", "timestamp": "2025-09-11T12:24:16.605163", "function": "text", "video_id": "7547590859831790898", "title": "只要10秒，你的声音就是我的了。 #AI作品整活大赏 #2025开学季 #AI新星计划  #开学的精选", "download_url": "https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000d2v6p4fog65u1oea9p3g&ratio=720p&line=0", "text_content": "离了个大谱，我们同事的声音被偷去打保健品的广告了，这是他的声音。大家好，我是上文清的弹幕利多，我现在在北京的国家自豪感冰丝带，这是广告的声音。如果你每天都到了要奖励的程度，那么这个牡蛎精就不要买了。另一个同事志威编辑部大饼声音也被扒去做克隆了。下面咱给大伙儿放两段录音，我敢保证你肯定听不出哪个是埃。微信蓝包这个送礼物的正经功能现在正成为抽象乐的人的天堂。这是真让我开了眼了。2.2吨的极氪001用255的宽胎。答案是，第一段是咱们知威原声，第二段是别人搞的AI声音克隆大饼的声音还不止被一家自媒体盗用了，有的在说车，有的直接干起了前沿部的活儿，咱们不是啥声优，也不是名人歌手，可现在一天的AI已经能用五六秒的样本复刻盗用任何一个普通人的声音了。看完这些视频，大饼都有点怀疑自己是不是真干过这个配音兼职。我们也查了不少资料，发现AI声音克隆虽然看起来刚成熟不久，变现的赛道上却已经拥挤的很了。首先上游的工具准备阶段，卖铲子的老套路依旧在转，森音克隆的工具其实不少都是免费的，像eleven laps各种Githu开源模型等等材料也是免费的。从各个网站视频里发名人大V的语音就行，只要十秒左右的高质量音频。不过后续任何内容的生成了教程还是免费的。视频平台上搜索一下声音克隆关键词，相关的教程五花八门，从复杂的开源部署到简单打开网页一键生成，效果看起来一个比一个屌。把这些免费的工具、素材、教程一块打包，就能轻松卖个几十块，这些技术含量极低的玩意儿，少则卖个几十单，多的居然卖了二百多单。商品详情页普遍打着只要几秒，样本相似度高达90%以上，定制任何语音、情感高度还原等等宣传语点进去就是一波焦虑。营销号称是做自媒体必备，而到了下游用AI声音克隆做什么样的自媒体才是真正赚钱的地方？利用明星的知名度和大伙儿的热情，用AI克隆明星声音成了最快的起号办法。他们甚至还把干和吸粉经验总结起来，把平台的雷剧和爽点都玩透了。不想被封就用杭州薛之谦，东北权志龙这种擦边账号名，官方人物碰不得，绝对不要号称是本人，想长期养号就得多个明星声音混剪，搞点方言，用老明星声音钓鱼网友怀旧情怀，良心一点的不拿去盈利，倒也还好，像是用明星的声音去翻唱各种歌，有的真是纯粉丝用爱发电，更恶心一点的就打着明星和家人的旗号开始直播带烂货圈钱了。比如前一阵子央视也报道了声音克隆的乱象，有的视频开局一张图在盗用体育明星的声音，总得给家乡各种农副产品做代言。一堆网友以为真是本人，纷纷下单支持，直接卖出了4.7万单秋逸，全被灰钱吃干抹净，风险却甩锅给明星本人。粉丝以为是偶像开新号，稀里糊涂就关注了消费者买到烂货，骂声全冲着政府去。只能说真是不能低估声音的影响力啊。我们也尝试了一下声音克隆流程，相当简单，不用花钱也不用技术。有个叫nice voice的网站，只要上传一段原声和目标文本，几秒钟就能免费出结果。你好，我是大饼。然而克隆成本级别的另一方面，AI声音分辨检测是真的又贵又难选择，还特别少。比如这个叫AI voice的网站，在没有任何免费试用，效果未知的情况下，检测AI声音的价格最低是一个月17.8美元。也就是说，别人花五十多终身使用甚至免费搞出来的东西，我们要尝试去证明一个月就要花一百多。免费的检测倒是也有，和免费的AI声音克隆各有各的强。免费的AI检测软件各有各的啦。像是这个嗨插件测那种明显的合成声，像是哈基米啊，一口气看完系列啊，没什么问题。一到了当前版本的hard的难度，他就开始宕机了。我们先用海雅测了一下差评监目的配音直接拿下了827的高分，还以为这下总能打脸那些质疑我们AI配音的网友了，结果大饼的AI配音讹了96，好嘛，小丑竟是我自己，这回真洗不清了。我们又试了一个号称见AI 90%以上准确率的网站，也没听出大饼声音是合成的，包括做声音克隆出身的eleven，即使已有相关技术的积累，也听不出是AI所以为啥现在分辨AI声音克隆这么难？造成这个局面的一方面是因为AI声音合成技术已经有点强的过分了，过去的AI语音其实有很多明显的破绽，就算不上检测器人也听得出来。比如声谱里能看到各种异常条纹、不自然的颤音、奇怪的噪音等等。现在的克隆技术使用大量的语音数据进行训练，又把音色、情感、语调、节奏等等维度拆开建模。这样深沉的声音不光音色像，连停顿、重音、情绪的细节都觉得人味十足。于是，过去用来分辨真假特征消除了。而与克隆技术飞速发展相对的另一面，检测工具搞得实在太被动了。要等新的克隆技术出现，花时间采集新数据，重新训练模型检测手段才可能有效。而很多实验阶段的检测器根本不更新，用的还是老数据集里的一尔甲。像是这个最近还在更新的检测项目，我们下载了他们最新的数据集，里面的AI样本却是这样。Now you see why brownie beaver would no more have thought of building his house and dry land. 明显已经过时了。毛泽盾的不均衡发展让现在AI声音克隆正在逐渐失控，背离发展的初心。比如对于一些发生困难的人，AI克隆是他们重新说话的希望，像是演员李雪坚老师之前就通过AI帮助修复声音，继续表演创作。可惜的是，这项刚刚成熟的技术还没等来完善的使用规范，就已经被人钻了空子。我们咨询了法务冯律师，她表示，根据民法典第1023条明确规定，自然人的声音保护参照肖像权保护的规定，声音作为人格权益的一部分，具有人身专属性，未经许可不得擅自使用、复制或商业化利用。然而，受害者的自证要比侵权麻烦的多。A声音克隆正在走向和迪换脸一样的结局。对于我们这些普通人来说，一些靠声纹识别的安全技术，比如要念出来的验证码，以后还会有效吗？给银行打电话，他们还能确认是我本人？虽然现在声音是被道具给视频配音倒是不算啥大事，但可以预见我们距离这样的电话已经很近了。是我是我妈，我现在急需用钱，能先给我转1万块吗？", "status": "success"}]