#!/usr/bin/env python3
"""
提取视频核心内容工具
去除废话，每个视频单独提炼核心要点
"""

import os
import sys
import asyncio
import time
from datetime import datetime
from pathlib import Path

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-b00e316a1bfe4bf2996bacf5808f2f96'

# 导入AI服务
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.ai_service import AIService
    from app.utils.text_parser import TextParser
    from app.models.requests import AnalysisType, ModelProvider
    print("✅ 成功导入AI服务模块")
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)

class CoreExtractor:
    """核心内容提取器"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.text_parser = TextParser()
        self.results = []
        self.success_count = 0
        self.error_count = 0
    
    def get_core_extraction_prompt(self):
        """获取核心提取的专用提示词"""
        return """
请从以下视频内容中提取最核心的要点，要求：

1. 只保留最重要的事实和信息
2. 去除所有废话、重复内容和无关信息
3. 用简洁的中文表达
4. 每个要点用一句话概括
5. 最多5个要点
6. 不要添加任何格式符号、标题或解释

文本内容：
{text}

核心要点：
"""
    
    async def extract_single_core(self, item, index, total):
        """提取单个视频的核心内容"""
        print(f"\n🔄 处理第 {index}/{total} 个视频...")
        print(f"📺 {item.title[:60]}...")
        
        result = {
            'index': index,
            'video_id': item.video_id,
            'title': item.title,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # 组合标题和内容
            full_text = f"标题: {item.title}\n内容: {item.content}"
            
            print(f"🤖 正在提取核心要点...")
            
            # 使用自定义提示词
            core_content, token_usage, model_used = await self.ai_service.analyze_text(
                text=full_text,
                analysis_type=AnalysisType.SUMMARY,
                model_provider=ModelProvider.DASHSCOPE,
                custom_prompt=self.get_core_extraction_prompt()
            )
            
            # 清理结果，去除多余的格式
            core_content = core_content.strip()
            # 去除可能的标题格式
            lines = core_content.split('\n')
            clean_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('**') and not line.startswith('---'):
                    # 去除序号格式
                    if line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•')):
                        line = line[2:].strip()
                    clean_lines.append(line)
            
            core_content = '\n'.join(clean_lines[:5])  # 最多5个要点
            
            result.update({
                'status': 'success',
                'core_content': core_content,
                'model_used': model_used,
                'processing_time': token_usage.get('processing_time', 0),
                'token_usage': token_usage.get('total_tokens', 0)
            })
            
            self.success_count += 1
            print(f"✅ 提取成功")
            
        except Exception as e:
            result.update({
                'status': 'error',
                'error': str(e)
            })
            self.error_count += 1
            print(f"❌ 提取失败: {str(e)}")
        
        self.results.append(result)
        return result
    
    async def batch_extract_cores(self, file_path, delay=1):
        """批量提取核心内容"""
        print(f"📄 解析文件: {file_path}")
        
        # 解析文件
        try:
            items = self.text_parser.parse_extracted_texts_file(file_path)
            print(f"✅ 成功解析 {len(items)} 个视频文本")
        except Exception as e:
            print(f"❌ 文件解析失败: {e}")
            return []
        
        if not items:
            print("❌ 文件中没有找到有效内容")
            return []
        
        print(f"\n🚀 开始提取 {len(items)} 个视频的核心内容")
        print(f"🎯 目标: 去除废话，只保留核心要点")
        print(f"🤖 使用模型: 通义千问")
        print("=" * 60)
        
        start_time = time.time()
        
        # 逐个处理
        for i, item in enumerate(items, 1):
            await self.extract_single_core(item, i, len(items))
            
            # 添加延时避免API限制
            if i < len(items):
                print(f"⏳ 等待 {delay} 秒...")
                await asyncio.sleep(delay)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print(f"🎉 核心提取完成！")
        print(f"📊 总计: {len(items)} 个视频")
        print(f"✅ 成功: {self.success_count} 个")
        print(f"❌ 失败: {self.error_count} 个")
        print(f"⏱️ 总耗时: {duration:.1f} 秒")
        
        return self.results
    
    def save_individual_cores(self, output_dir="video_cores"):
        """为每个视频单独保存核心内容"""
        # 创建输出目录
        Path(output_dir).mkdir(exist_ok=True)
        
        saved_files = []
        
        for result in self.results:
            if result['status'] == 'success':
                # 创建安全的文件名
                safe_title = "".join(c for c in result['title'][:50] if c.isalnum() or c in (' ', '-', '_')).strip()
                filename = f"{result['video_id']}_{safe_title}.txt"
                filepath = Path(output_dir) / filename
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(f"视频ID: {result['video_id']}\n")
                    f.write(f"标题: {result['title']}\n")
                    f.write(f"提取时间: {result['timestamp'][:19]}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write("核心要点:\n")
                    f.write(result['core_content'])
                    f.write("\n")
                
                saved_files.append(str(filepath))
        
        print(f"💾 已为 {len(saved_files)} 个视频单独保存核心内容到: {output_dir}/")
        return saved_files
    
    def save_summary_file(self, filename=None):
        """保存汇总文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"核心内容汇总_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("🎯 视频核心内容汇总\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"📊 统计信息:\n")
            f.write(f"   总视频数: {len(self.results)}\n")
            f.write(f"   成功提取: {self.success_count}\n")
            f.write(f"   失败数量: {self.error_count}\n")
            f.write(f"   提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            success_results = [r for r in self.results if r['status'] == 'success']
            
            for i, result in enumerate(success_results, 1):
                f.write(f"{i}. 【{result['video_id']}】\n")
                f.write(f"   {result['title']}\n")
                f.write(f"   核心要点: {result['core_content']}\n")
                f.write("-" * 40 + "\n\n")
        
        print(f"📋 汇总文件已保存: {filename}")
        return filename

async def main():
    """主函数"""
    print("🎯 视频核心内容提取工具")
    print("=" * 50)
    print("🎯 功能: 去除废话，只保留最核心的要点")
    print("📁 输出: 每个视频单独一个文件 + 汇总文件")
    
    extractor = CoreExtractor()
    
    # 1. 检查AI服务
    print("\n1. 检查AI服务状态...")
    if not extractor.ai_service.is_available(ModelProvider.DASHSCOPE):
        print("❌ 通义千问服务不可用")
        return
    else:
        print("✅ 通义千问服务可用")
    
    # 2. 检查文件
    file_path = "extracted_texts_20250911_122444.txt"
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print(f"✅ 找到文件: {file_path}")
    
    # 3. 设置延时
    delay = float(input("\n请输入处理间隔秒数 (默认1秒): ").strip() or "1")
    
    # 4. 开始提取
    print(f"\n🚀 开始提取核心内容...")
    results = await extractor.batch_extract_cores(file_path, delay)
    
    if results:
        # 5. 保存单独文件
        individual_files = extractor.save_individual_cores()
        
        # 6. 保存汇总文件
        summary_file = extractor.save_summary_file()
        
        # 7. 显示预览
        print(f"\n📝 前3个核心内容预览:")
        print("-" * 50)
        
        success_results = [r for r in results if r['status'] == 'success']
        for i, result in enumerate(success_results[:3], 1):
            print(f"\n{i}. 【{result['video_id']}】")
            print(f"   标题: {result['title'][:60]}...")
            print(f"   核心: {result['core_content'][:100]}...")
        
        print(f"\n🎉 完成！")
        print(f"📁 单独文件: video_cores/ 目录下")
        print(f"📋 汇总文件: {summary_file}")
    else:
        print("❌ 没有成功的提取结果")

if __name__ == "__main__":
    asyncio.run(main())
