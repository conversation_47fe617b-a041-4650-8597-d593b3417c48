"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    """应用设置类"""
    
    # 应用基础配置
    APP_NAME: str = "AI文本信息提炼API"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # API密钥配置
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    
    # 大模型配置
    DEFAULT_MODEL_PROVIDER: str = "openai"  # openai, anthropic
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    ANTHROPIC_MODEL: str = "claude-3-haiku-20240307"
    
    # 文本处理配置
    MAX_TEXT_LENGTH: int = 50000  # 单次处理最大文本长度
    MAX_BATCH_SIZE: int = 10      # 批量处理最大数量
    
    # 文件配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "app.log"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保上传目录存在
        Path(self.UPLOAD_DIR).mkdir(exist_ok=True)

# 创建全局设置实例
settings = Settings()

# 验证必要的API密钥
def validate_api_keys():
    """验证API密钥配置"""
    if not settings.OPENAI_API_KEY and not settings.ANTHROPIC_API_KEY:
        raise ValueError(
            "至少需要配置一个AI服务的API密钥。请在.env文件中设置 OPENAI_API_KEY 或 ANTHROPIC_API_KEY"
        )
    
    if settings.DEFAULT_MODEL_PROVIDER == "openai" and not settings.OPENAI_API_KEY:
        raise ValueError("使用OpenAI服务需要设置 OPENAI_API_KEY")
    
    if settings.DEFAULT_MODEL_PROVIDER == "anthropic" and not settings.ANTHROPIC_API_KEY:
        raise ValueError("使用Anthropic服务需要设置 ANTHROPIC_API_KEY")

# 在导入时验证配置
try:
    validate_api_keys()
except ValueError as e:
    print(f"⚠️  配置警告: {e}")
    print("💡 请创建 .env 文件并配置相应的API密钥")
