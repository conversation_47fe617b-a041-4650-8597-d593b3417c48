#!/usr/bin/env python3
"""
文档内容提炼脚本
专门用于提炼extracted_texts文件的核心内容
"""

import requests
import json
import time
from pathlib import Path

class ContentExtractor:
    """内容提炼器"""
    
    def __init__(self, api_base_url="http://127.0.0.1:8000"):
        self.api_base_url = api_base_url
    
    def check_api_status(self):
        """检查API服务状态"""
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=5)
            return response.json()
        except requests.exceptions.ConnectionError:
            return {"error": "无法连接到API服务"}
        except Exception as e:
            return {"error": str(e)}
    
    def extract_from_file(self, file_path, analysis_type="summary"):
        """从文件提取内容"""
        if not Path(file_path).exists():
            return {"error": f"文件不存在: {file_path}"}
        
        try:
            url = f"{self.api_base_url}/api/v1/analyze/file"
            
            with open(file_path, 'rb') as f:
                files = {"file": f}
                data = {
                    "analysis_type": analysis_type,
                    "model_provider": "dashscope"
                }
                
                print(f"📤 正在上传文件并分析...")
                response = requests.post(url, files=files, data=data, timeout=120)
                return response.json()
                
        except Exception as e:
            return {"error": str(e)}
    
    def extract_from_text(self, text, analysis_type="summary"):
        """从文本提取内容"""
        try:
            url = f"{self.api_base_url}/api/v1/analyze/text"
            data = {
                "text": text,
                "analysis_type": analysis_type,
                "model_provider": "dashscope"
            }
            
            response = requests.post(url, json=data, timeout=60)
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}
    
    def batch_extract(self, texts, analysis_type="summary"):
        """批量提取内容"""
        try:
            url = f"{self.api_base_url}/api/v1/analyze/batch"
            data = {
                "texts": texts,
                "analysis_type": analysis_type,
                "model_provider": "dashscope"
            }
            
            response = requests.post(url, json=data, timeout=120)
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}
    
    def custom_extract(self, text, custom_prompt):
        """自定义提示词提取"""
        try:
            url = f"{self.api_base_url}/api/v1/analyze/custom"
            data = {
                "text": text,
                "prompt": custom_prompt,
                "model_provider": "dashscope"
            }
            
            response = requests.post(url, json=data, timeout=60)
            return response.json()
            
        except Exception as e:
            return {"error": str(e)}

def save_results_to_file(results, output_file="extracted_results.txt"):
    """保存结果到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 60 + "\n")
        f.write("AI文本信息提炼结果\n")
        f.write("=" * 60 + "\n\n")
        
        if isinstance(results, dict) and results.get("success"):
            if "results" in results:  # 文件分析结果
                f.write(f"总视频数: {results['total_videos']}\n")
                f.write(f"成功处理: {results['successful_count']}\n")
                f.write(f"处理时间: {results['total_processing_time']:.2f}秒\n\n")
                
                for i, item in enumerate(results['results'], 1):
                    if item['success']:
                        f.write(f"{i}. 视频ID: {item['video_id']}\n")
                        f.write(f"   标题: {item['title']}\n")
                        f.write(f"   分析结果:\n{item['analysis_result']}\n")
                        f.write("-" * 40 + "\n\n")
            else:  # 单文本分析结果
                data = results['data']
                f.write(f"分析类型: {data['analysis_type']}\n")
                f.write(f"使用模型: {data['model_used']}\n")
                f.write(f"处理时间: {data['processing_time']:.2f}秒\n\n")
                f.write(f"分析结果:\n{data['result']}\n")
        else:
            f.write(f"分析失败: {results.get('error', '未知错误')}\n")
    
    print(f"✅ 结果已保存到: {output_file}")

def main():
    """主函数"""
    print("🤖 AI文档内容提炼工具")
    print("=" * 50)
    
    extractor = ContentExtractor()
    
    # 检查API服务状态
    print("1. 检查API服务状态...")
    status = extractor.check_api_status()
    if "error" in status:
        print(f"❌ {status['error']}")
        print("\n💡 请先启动API服务:")
        print("   python start_api.py")
        return
    else:
        print(f"✅ API服务运行正常")
    
    # 检查文件是否存在
    file_path = "extracted_texts_20250911_122444.txt"
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        print("💡 请确保文件在当前目录下")
        return
    
    print(f"\n2. 开始分析文件: {file_path}")
    
    # 提供分析选项
    print("\n📋 请选择分析类型:")
    print("1. summary - 生成摘要（推荐）")
    print("2. extract - 提取关键信息")
    print("3. keywords - 提取关键词")
    print("4. classify - 主题分类")
    print("5. sentiment - 情感分析")
    print("6. custom - 自定义分析")
    
    choice = input("\n请输入选择 (1-6，默认1): ").strip() or "1"
    
    analysis_types = {
        "1": "summary",
        "2": "extract", 
        "3": "keywords",
        "4": "classify",
        "5": "sentiment"
    }
    
    if choice in analysis_types:
        analysis_type = analysis_types[choice]
        print(f"\n🔍 开始{analysis_type}分析...")
        
        start_time = time.time()
        result = extractor.extract_from_file(file_path, analysis_type)
        end_time = time.time()
        
        if result.get("success"):
            print(f"✅ 分析完成! 耗时: {end_time - start_time:.2f}秒")
            print(f"📊 处理了 {result['total_videos']} 个视频")
            print(f"📈 成功: {result['successful_count']}, 失败: {result['failed_count']}")
            
            # 保存结果
            output_file = f"{analysis_type}_results_{int(time.time())}.txt"
            save_results_to_file(result, output_file)
            
            # 显示前3个结果预览
            print(f"\n📝 前3个结果预览:")
            for i, item in enumerate(result['results'][:3], 1):
                if item['success']:
                    print(f"\n{i}. {item['title'][:50]}...")
                    print(f"   {item['analysis_result'][:150]}...")
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
    
    elif choice == "6":
        # 自定义分析
        print("\n📝 自定义分析模式")
        custom_prompt = input("请输入自定义分析提示词: ").strip()
        
        if custom_prompt:
            # 读取文件内容的前1000字符作为示例
            with open(file_path, 'r', encoding='utf-8') as f:
                sample_text = f.read()[:2000]
            
            print(f"\n🔍 使用自定义提示词分析...")
            result = extractor.custom_extract(sample_text, custom_prompt)
            
            if result.get("success"):
                print(f"✅ 自定义分析完成!")
                output_file = f"custom_results_{int(time.time())}.txt"
                save_results_to_file(result, output_file)
                print(f"\n📝 分析结果:\n{result['data']['result'][:300]}...")
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
        else:
            print("❌ 请输入有效的提示词")
    
    else:
        print("❌ 无效选择")
    
    print(f"\n🎉 处理完成!")
    print(f"💡 你也可以访问 http://127.0.0.1:8000/docs 使用Web界面")

if __name__ == "__main__":
    main()
