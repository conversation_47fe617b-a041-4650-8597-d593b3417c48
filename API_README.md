# AI文本信息提炼API

基于大模型的智能文本分析服务，专门用于处理抖音视频提取的文字内容，提供文本摘要、关键信息提取、主题分类等功能。

## 🚀 功能特性

- **多种分析类型**: 支持文本摘要、关键信息提取、主题分类、情感分析、关键词提取
- **多AI服务支持**: 集成OpenAI GPT、Anthropic Claude和通义千问模型
- **批量处理**: 支持批量文本分析，提高处理效率
- **文件上传**: 直接上传extracted_texts文件进行分析
- **自定义提示词**: 支持用户自定义分析提示词
- **RESTful API**: 标准的REST API接口，易于集成
- **自动文档**: 自动生成Swagger和ReDoc API文档

## 📋 系统要求

- Python 3.8+
- FastAPI
- OpenAI API密钥、Anthropic API密钥 或 通义千问API密钥（至少一个）

## 🛠️ 安装部署

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置API密钥：
```env
# 至少配置一个AI服务的API密钥
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# 其他配置
DEFAULT_MODEL_PROVIDER=dashscope
DEBUG=false
PORT=8000
```

### 3. 启动服务

```bash
python main.py
```

或使用uvicorn：
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 📖 API文档

启动服务后，访问以下地址查看API文档：

- 主页: http://localhost:8000/
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🔧 API使用示例

### 1. 单文本分析

```python
import requests

url = "http://localhost:8000/api/v1/analyze/text"
data = {
    "text": "这是要分析的文本内容...",
    "analysis_type": "summary",
    "model_provider": "openai"
}

response = requests.post(url, json=data)
result = response.json()
print(result)
```

### 2. 批量文本分析

```python
import requests

url = "http://localhost:8000/api/v1/analyze/batch"
data = {
    "texts": [
        "第一段文本内容...",
        "第二段文本内容...",
        "第三段文本内容..."
    ],
    "analysis_type": "extract",
    "model_provider": "dashscope"
}

response = requests.post(url, json=data)
result = response.json()
print(result)
```

### 3. 文件上传分析

```python
import requests

url = "http://localhost:8000/api/v1/analyze/file"
files = {"file": open("extracted_texts_20250911_122444.txt", "rb")}
data = {
    "analysis_type": "summary",
    "model_provider": "dashscope"
}

response = requests.post(url, files=files, data=data)
result = response.json()
print(result)
```

### 4. 自定义提示词分析

```python
import requests

url = "http://localhost:8000/api/v1/analyze/custom"
data = {
    "text": "要分析的文本内容...",
    "prompt": "请分析这段文本的商业价值和市场机会",
    "model_provider": "dashscope"
}

response = requests.post(url, json=data)
result = response.json()
print(result)
```

## 📊 分析类型说明

| 类型 | 值 | 描述 |
|------|-----|------|
| 摘要生成 | `summary` | 生成文本的核心摘要 |
| 关键信息提取 | `extract` | 提取重要信息和关键点 |
| 主题分类 | `classify` | 对文本进行主题分类 |
| 情感分析 | `sentiment` | 分析文本的情感倾向 |
| 关键词提取 | `keywords` | 提取文本的关键词 |

## 🔑 支持的AI模型

### OpenAI
- gpt-3.5-turbo (默认)
- gpt-4
- gpt-4-turbo

### Anthropic
- claude-3-haiku-20240307 (默认)
- claude-3-sonnet-20240229
- claude-3-opus-20240229

### 通义千问 (DashScope)
- qwen-plus-latest (默认)
- qwen-turbo-latest
- qwen-max-latest

## ⚙️ 配置说明

主要配置项：

```env
# 应用配置
DEBUG=false                    # 调试模式
HOST=0.0.0.0                  # 监听地址
PORT=8000                     # 监听端口

# AI服务配置
DEFAULT_MODEL_PROVIDER=dashscope  # 默认AI服务商
OPENAI_MODEL=gpt-3.5-turbo    # OpenAI模型
ANTHROPIC_MODEL=claude-3-haiku-20240307  # Anthropic模型
DASHSCOPE_MODEL=qwen-plus-latest  # 通义千问模型

# 处理限制
MAX_TEXT_LENGTH=50000         # 单次处理最大文本长度
MAX_BATCH_SIZE=10            # 批量处理最大数量
MAX_FILE_SIZE=10485760       # 最大文件大小(10MB)

# 日志配置
LOG_LEVEL=INFO               # 日志级别
LOG_FILE=app.log            # 日志文件名
```

## 🚨 错误处理

API返回标准的HTTP状态码：

- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `429`: 请求频率限制
- `500`: 服务器内部错误

错误响应格式：
```json
{
    "success": false,
    "error": "错误描述",
    "error_code": "ERROR_CODE",
    "timestamp": "2025-01-11T12:00:00"
}
```

## 📝 日志

应用日志保存在 `logs/` 目录下：

- `app.log`: 应用日志
- `error.log`: 错误日志

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License
