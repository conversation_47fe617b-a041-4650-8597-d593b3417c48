#!/usr/bin/env python3
"""
AI文本信息提炼API演示脚本
展示项目的核心功能
"""

import sys
from pathlib import Path

def demo_text_parser():
    """演示文本解析功能"""
    print("🔍 演示文本解析功能")
    print("-" * 30)
    
    try:
        from app.utils.text_parser import TextParser
        
        # 检查示例文件是否存在
        sample_file = "extracted_texts_20250911_122444.txt"
        if not Path(sample_file).exists():
            print(f"❌ 示例文件 {sample_file} 不存在")
            return
        
        parser = TextParser()
        
        # 解析文件
        print(f"📄 解析文件: {sample_file}")
        items = parser.parse_extracted_texts_file(sample_file)
        
        print(f"✅ 成功解析 {len(items)} 个视频文本")
        
        # 显示统计信息
        stats = parser.get_text_statistics(items)
        print(f"📊 统计信息:")
        print(f"   - 总视频数: {stats['total_videos']}")
        print(f"   - 总字符数: {stats['total_characters']}")
        print(f"   - 平均长度: {stats['average_length']}")
        print(f"   - 最长文本: {stats['longest_text']}")
        print(f"   - 最短文本: {stats['shortest_text']}")
        
        # 显示前3个解析结果
        print(f"\n📝 前3个解析结果:")
        for i, item in enumerate(items[:3]):
            print(f"\n{i+1}. 视频ID: {item.video_id}")
            print(f"   标题: {item.title[:50]}...")
            print(f"   内容: {item.content[:100]}...")
        
        return items
        
    except Exception as e:
        print(f"❌ 文本解析演示失败: {e}")
        return None

def demo_ai_prompts():
    """演示AI提示词功能"""
    print("\n🤖 演示AI提示词功能")
    print("-" * 30)
    
    try:
        from app.services.ai_service import AIService
        from app.models.requests import AnalysisType
        
        ai_service = AIService()
        
        # 展示不同类型的提示词
        analysis_types = [
            AnalysisType.SUMMARY,
            AnalysisType.EXTRACT,
            AnalysisType.KEYWORDS,
            AnalysisType.SENTIMENT,
            AnalysisType.CLASSIFY
        ]
        
        sample_text = "这是一个示例文本"
        
        for analysis_type in analysis_types:
            prompt = ai_service.get_analysis_prompt(analysis_type)
            print(f"\n📋 {analysis_type.value} 分析提示词:")
            print(prompt.format(text=sample_text)[:200] + "...")
        
        print("✅ 提示词演示完成")
        
    except Exception as e:
        print(f"❌ AI提示词演示失败: {e}")

def demo_config():
    """演示配置管理功能"""
    print("\n⚙️ 演示配置管理功能")
    print("-" * 30)
    
    try:
        from app.config import settings
        
        print(f"📋 当前配置:")
        print(f"   - 应用名称: {settings.APP_NAME}")
        print(f"   - 版本: {settings.VERSION}")
        print(f"   - 调试模式: {settings.DEBUG}")
        print(f"   - 监听地址: {settings.HOST}:{settings.PORT}")
        print(f"   - 默认AI提供商: {settings.DEFAULT_MODEL_PROVIDER}")
        print(f"   - OpenAI模型: {settings.OPENAI_MODEL}")
        print(f"   - Anthropic模型: {settings.ANTHROPIC_MODEL}")
        print(f"   - 最大文本长度: {settings.MAX_TEXT_LENGTH}")
        print(f"   - 最大批量大小: {settings.MAX_BATCH_SIZE}")
        print(f"   - 最大文件大小: {settings.MAX_FILE_SIZE}")
        
        # 检查API密钥配置状态
        has_openai = bool(settings.OPENAI_API_KEY)
        has_anthropic = bool(settings.ANTHROPIC_API_KEY)
        
        print(f"\n🔑 API密钥状态:")
        print(f"   - OpenAI: {'✅ 已配置' if has_openai else '❌ 未配置'}")
        print(f"   - Anthropic: {'✅ 已配置' if has_anthropic else '❌ 未配置'}")
        
        if not has_openai and not has_anthropic:
            print("⚠️  警告: 没有配置任何AI服务的API密钥")
            print("💡 请创建 .env 文件并配置相应的API密钥")
        
        print("✅ 配置演示完成")
        
    except Exception as e:
        print(f"❌ 配置演示失败: {e}")

def demo_models():
    """演示数据模型功能"""
    print("\n📊 演示数据模型功能")
    print("-" * 30)
    
    try:
        from app.models.requests import TextAnalysisRequest, AnalysisType, ModelProvider
        from app.models.responses import TextAnalysisResponse, AnalysisResult
        
        # 创建请求模型示例
        request = TextAnalysisRequest(
            text="这是一个测试文本，用于演示数据模型功能。",
            analysis_type=AnalysisType.SUMMARY,
            model_provider=ModelProvider.OPENAI
        )
        
        print(f"📝 请求模型示例:")
        print(f"   - 文本长度: {len(request.text)}")
        print(f"   - 分析类型: {request.analysis_type}")
        print(f"   - 模型提供商: {request.model_provider}")
        
        # 创建响应模型示例
        analysis_result = AnalysisResult(
            result="这是分析结果示例",
            analysis_type="summary",
            model_used="gpt-3.5-turbo",
            processing_time=1.23,
            token_usage={"total_tokens": 100}
        )
        
        response = TextAnalysisResponse(
            success=True,
            data=analysis_result
        )
        
        print(f"\n📤 响应模型示例:")
        print(f"   - 成功状态: {response.success}")
        print(f"   - 分析结果: {response.data.result}")
        print(f"   - 使用模型: {response.data.model_used}")
        print(f"   - 处理时间: {response.data.processing_time}秒")
        
        print("✅ 数据模型演示完成")
        
    except Exception as e:
        print(f"❌ 数据模型演示失败: {e}")

def show_project_info():
    """显示项目信息"""
    print("🤖 AI文本信息提炼API项目演示")
    print("=" * 50)
    print("📋 项目功能:")
    print("   ✅ 多种文本分析类型 (摘要、提取、分类、情感、关键词)")
    print("   ✅ 支持OpenAI和Anthropic AI服务")
    print("   ✅ 批量文本处理")
    print("   ✅ 文件上传分析")
    print("   ✅ 自定义提示词")
    print("   ✅ RESTful API接口")
    print("   ✅ 自动生成API文档")
    print()

def show_next_steps():
    """显示后续步骤"""
    print("\n🚀 后续步骤:")
    print("1. 配置API密钥:")
    print("   cp .env.example .env")
    print("   # 编辑 .env 文件，配置 OPENAI_API_KEY 或 ANTHROPIC_API_KEY")
    print()
    print("2. 启动API服务:")
    print("   python start_api.py")
    print("   # 或者: python main.py")
    print()
    print("3. 测试API功能:")
    print("   python test_client.py")
    print()
    print("4. 查看API文档:")
    print("   浏览器访问: http://localhost:8000/docs")
    print()
    print("📚 详细文档:")
    print("   - API使用文档: API_README.md")
    print("   - 项目结构说明: PROJECT_STRUCTURE.md")

def main():
    """主函数"""
    show_project_info()
    
    # 运行各种演示
    demo_config()
    demo_models()
    demo_text_parser()
    demo_ai_prompts()
    
    show_next_steps()
    
    print("\n🎉 演示完成！")

if __name__ == "__main__":
    main()
