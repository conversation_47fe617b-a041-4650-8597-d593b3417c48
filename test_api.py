#!/usr/bin/env python3
"""
测试API服务
"""

import requests
import json
import time

def test_api_connection():
    """测试API连接"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.json()
    except requests.exceptions.ConnectionError:
        return {"error": "无法连接到API服务，请确保服务已启动"}
    except Exception as e:
        return {"error": str(e)}

def test_models_status():
    """测试模型状态"""
    try:
        response = requests.get("http://localhost:8000/api/v1/models/status", timeout=5)
        return response.json()
    except Exception as e:
        return {"error": str(e)}

def test_text_analysis():
    """测试文本分析"""
    try:
        url = "http://localhost:8000/api/v1/analyze/text"
        data = {
            "text": "这是一个测试文本，用于验证通义千问模型的文本分析功能。",
            "analysis_type": "summary",
            "model_provider": "dashscope"
        }
        
        response = requests.post(url, json=data, timeout=30)
        return response.json()
    except Exception as e:
        return {"error": str(e)}

def main():
    print("🧪 API服务测试")
    print("=" * 40)
    
    # 测试连接
    print("1. 测试API连接...")
    health = test_api_connection()
    if "error" in health:
        print(f"❌ {health['error']}")
        print("\n💡 请先启动API服务:")
        print("   python main.py")
        print("   或者: python start_api.py")
        return
    else:
        print(f"✅ API服务运行正常: {health.get('message', '')}")
    
    # 测试模型状态
    print("\n2. 测试模型状态...")
    models = test_models_status()
    if "error" in models:
        print(f"❌ {models['error']}")
    else:
        data = models.get("data", {})
        print(f"✅ 通义千问可用: {data.get('dashscope', {}).get('available', False)}")
        print(f"✅ 默认提供商: {data.get('default_provider', 'unknown')}")
    
    # 测试文本分析
    print("\n3. 测试文本分析...")
    result = test_text_analysis()
    if "error" in result:
        print(f"❌ {result['error']}")
    elif result.get("success"):
        print("✅ 文本分析成功!")
        data = result.get("data", {})
        print(f"   模型: {data.get('model_used', 'unknown')}")
        print(f"   处理时间: {data.get('processing_time', 0):.2f}秒")
        print(f"   分析结果: {data.get('result', '')[:100]}...")
    else:
        print(f"❌ 文本分析失败: {result.get('error', '未知错误')}")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
