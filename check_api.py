#!/usr/bin/env python3
"""
检查API服务状态
"""

import requests
import socket
import subprocess
import sys

def check_port_in_use(port):
    """检查端口是否被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return False
        except OSError:
            return True

def check_process_on_port(port):
    """检查端口上运行的进程"""
    try:
        if sys.platform == "win32":
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) > 4:
                        pid = parts[-1]
                        return f"PID: {pid}"
        else:
            result = subprocess.run(['lsof', '-i', f':{port}'], capture_output=True, text=True)
            return result.stdout
    except:
        pass
    return None

def test_api_endpoints():
    """测试API端点"""
    base_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://0.0.0.0:8000"
    ]
    
    for base_url in base_urls:
        print(f"\n🔍 测试: {base_url}")
        try:
            # 测试健康检查
            response = requests.get(f"{base_url}/health", timeout=5)
            print(f"  ✅ /health: {response.status_code} - {response.json()}")
            
            # 测试模型状态
            response = requests.get(f"{base_url}/api/v1/models/status", timeout=5)
            print(f"  ✅ /models/status: {response.status_code}")
            
            return base_url
            
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接失败")
        except requests.exceptions.Timeout:
            print(f"  ❌ 超时")
        except Exception as e:
            print(f"  ❌ 错误: {e}")
    
    return None

def main():
    print("🔧 API服务诊断工具")
    print("=" * 50)
    
    # 1. 检查端口占用
    port = 8000
    print(f"1. 检查端口 {port} 是否被占用...")
    if check_port_in_use(port):
        print(f"  ✅ 端口 {port} 被占用")
        process_info = check_process_on_port(port)
        if process_info:
            print(f"  📋 进程信息: {process_info}")
    else:
        print(f"  ❌ 端口 {port} 未被占用")
        print("  💡 API服务可能没有启动")
    
    # 2. 测试API端点
    print(f"\n2. 测试API端点...")
    working_url = test_api_endpoints()
    
    if working_url:
        print(f"\n✅ 找到可用的API服务: {working_url}")
        
        # 3. 测试文本分析
        print(f"\n3. 测试文本分析功能...")
        try:
            test_data = {
                "text": "这是一个测试文本",
                "analysis_type": "summary",
                "model_provider": "dashscope"
            }
            
            response = requests.post(
                f"{working_url}/api/v1/analyze/text", 
                json=test_data, 
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print("  ✅ 文本分析功能正常")
                    print(f"  📊 使用模型: {result['data']['model_used']}")
                else:
                    print(f"  ❌ 分析失败: {result.get('error')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                print(f"  📄 响应: {response.text}")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    else:
        print(f"\n❌ 无法找到可用的API服务")
        print(f"\n💡 解决方案:")
        print(f"   1. 确保API服务正在运行:")
        print(f"      python main.py")
        print(f"   2. 检查是否有错误信息")
        print(f"   3. 尝试重新启动服务")

if __name__ == "__main__":
    main()
