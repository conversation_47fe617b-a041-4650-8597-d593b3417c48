# 项目结构说明

## 📁 目录结构

```
ai-text-analysis-api/
├── app/                          # 应用核心代码
│   ├── __init__.py              # 应用包初始化
│   ├── config.py                # 配置管理
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── requests.py          # 请求数据模型
│   │   └── responses.py         # 响应数据模型
│   ├── services/                # 服务层
│   │   ├── __init__.py
│   │   └── ai_service.py        # AI服务集成
│   ├── routers/                 # API路由
│   │   ├── __init__.py
│   │   └── text_analysis.py     # 文本分析路由
│   └── utils/                   # 工具函数
│       ├── __init__.py
│       ├── text_parser.py       # 文本解析工具
│       ├── logger.py            # 日志配置
│       └── exceptions.py        # 自定义异常
├── logs/                        # 日志文件目录 (自动创建)
├── uploads/                     # 文件上传目录 (自动创建)
├── main.py                      # 应用入口文件
├── start_api.py                 # 启动脚本
├── test_client.py               # 测试客户端
├── requirements.txt             # Python依赖
├── .env.example                 # 环境变量模板
├── .env                         # 环境变量文件 (需要创建)
├── API_README.md                # API使用文档
├── PROJECT_STRUCTURE.md         # 项目结构说明 (本文件)
└── extracted_texts_20250911_122444.txt  # 示例数据文件
```

## 📋 文件说明

### 核心文件

- **main.py**: FastAPI应用的主入口文件，定义了应用实例和基本路由
- **start_api.py**: 便捷的启动脚本，包含依赖检查和配置验证
- **test_client.py**: API测试客户端，演示各种功能的使用方法

### 应用代码 (app/)

#### 配置管理
- **config.py**: 使用Pydantic管理应用配置，支持环境变量

#### 数据模型 (models/)
- **requests.py**: 定义API请求的数据模型和验证规则
- **responses.py**: 定义API响应的数据模型

#### 服务层 (services/)
- **ai_service.py**: 集成OpenAI和Anthropic API，提供统一的文本分析接口

#### 路由层 (routers/)
- **text_analysis.py**: 实现所有文本分析相关的API端点

#### 工具函数 (utils/)
- **text_parser.py**: 解析extracted_texts文件格式的工具
- **logger.py**: 日志配置和管理
- **exceptions.py**: 自定义异常类

### 配置文件

- **.env.example**: 环境变量配置模板
- **.env**: 实际的环境变量配置文件 (需要用户创建)
- **requirements.txt**: Python依赖包列表

### 文档文件

- **API_README.md**: 详细的API使用文档
- **PROJECT_STRUCTURE.md**: 项目结构说明文档

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件，配置API密钥
```

### 3. 启动服务
```bash
python start_api.py
```

或者直接使用：
```bash
python main.py
```

### 4. 测试API
```bash
python test_client.py
```

## 🔧 开发说明

### 添加新的分析类型

1. 在 `app/models/requests.py` 中的 `AnalysisType` 枚举添加新类型
2. 在 `app/services/ai_service.py` 中的 `get_analysis_prompt` 方法添加对应的提示词
3. 测试新功能

### 添加新的AI服务提供商

1. 在 `app/models/requests.py` 中的 `ModelProvider` 枚举添加新提供商
2. 在 `app/services/ai_service.py` 中添加对应的客户端和分析方法
3. 在 `app/config.py` 中添加相关配置项

### 自定义文本解析器

修改 `app/utils/text_parser.py` 中的 `TextParser` 类来支持不同的文件格式。

## 📊 API端点概览

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 主页 |
| `/health` | GET | 健康检查 |
| `/docs` | GET | Swagger API文档 |
| `/redoc` | GET | ReDoc API文档 |
| `/api/v1/analyze/text` | POST | 单文本分析 |
| `/api/v1/analyze/batch` | POST | 批量文本分析 |
| `/api/v1/analyze/file` | POST | 文件上传分析 |
| `/api/v1/analyze/custom` | POST | 自定义提示词分析 |
| `/api/v1/models/status` | GET | 获取AI模型状态 |

## 🔒 安全考虑

- API密钥通过环境变量管理，不在代码中硬编码
- 文件上传有大小限制和类型验证
- 文本长度有限制，防止过大请求
- 批量处理有数量限制
- 并发请求有限制，防止API滥用

## 📝 日志管理

- 应用日志: `logs/app.log`
- 错误日志: `logs/error.log`
- 日志级别可通过环境变量配置
- 支持控制台和文件双重输出

## 🧪 测试

使用 `test_client.py` 进行功能测试：

```bash
python test_client.py
```

测试包括：
- 健康检查
- 模型状态检查
- 单文本分析
- 批量文本分析
- 文件上传分析
- 自定义提示词分析

## 🚀 部署建议

### 开发环境
```bash
python start_api.py
```

### 生产环境
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

或使用Gunicorn：
```bash
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```
