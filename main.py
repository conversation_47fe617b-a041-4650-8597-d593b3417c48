"""
AI文本信息提炼API服务
主应用入口文件
"""

from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from pathlib import Path

from app.config import settings
from app.routers import text_analysis
from app.models.responses import HealthResponse

# 创建FastAPI应用实例
app = FastAPI(
    title="AI文本信息提炼API",
    description="基于大模型的文本信息提炼和分析服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(text_analysis.router, prefix="/api/v1", tags=["文本分析"])

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径，返回API介绍页面"""
    return """
    <html>
        <head>
            <title>AI文本信息提炼API</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 800px; margin: 0 auto; }
                .header { text-align: center; color: #333; }
                .feature { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; }
                .api-link { display: inline-block; margin: 10px; padding: 10px 20px; 
                           background: #007acc; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="header">🤖 AI文本信息提炼API</h1>
                <p class="header">基于大模型的智能文本分析服务</p>
                
                <div class="feature">
                    <h3>📝 核心功能</h3>
                    <ul>
                        <li>文本摘要生成</li>
                        <li>关键信息提取</li>
                        <li>主题分类</li>
                        <li>情感分析</li>
                        <li>批量文本处理</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>🚀 快速开始</h3>
                    <p>访问API文档了解详细使用方法：</p>
                    <a href="/docs" class="api-link">Swagger文档</a>
                    <a href="/redoc" class="api-link">ReDoc文档</a>
                </div>
                
                <div class="feature">
                    <h3>💡 使用示例</h3>
                    <p>POST /api/v1/analyze/summary - 生成文本摘要</p>
                    <p>POST /api/v1/analyze/extract - 提取关键信息</p>
                    <p>POST /api/v1/analyze/batch - 批量处理文本</p>
                </div>
            </div>
        </body>
    </html>
    """

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(
        status="healthy",
        message="AI文本信息提炼API服务运行正常",
        version="1.0.0"
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
