"""
API响应数据模型
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    message: str = Field(..., description="状态消息")
    version: str = Field(..., description="API版本")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class AnalysisResult(BaseModel):
    """分析结果模型"""
    result: str = Field(..., description="分析结果")
    analysis_type: str = Field(..., description="分析类型")
    model_used: str = Field(..., description="使用的AI模型")
    processing_time: float = Field(..., description="处理时间(秒)")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token使用情况")

class TextAnalysisResponse(BaseModel):
    """文本分析响应模型"""
    success: bool = Field(..., description="是否成功")
    data: Optional[AnalysisResult] = Field(None, description="分析结果")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class BatchAnalysisResult(BaseModel):
    """批量分析结果项"""
    index: int = Field(..., description="文本索引")
    result: str = Field(..., description="分析结果")
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")

class BatchAnalysisResponse(BaseModel):
    """批量文本分析响应模型"""
    success: bool = Field(..., description="整体是否成功")
    results: List[BatchAnalysisResult] = Field(..., description="批量分析结果")
    analysis_type: str = Field(..., description="分析类型")
    model_used: str = Field(..., description="使用的AI模型")
    total_processing_time: float = Field(..., description="总处理时间(秒)")
    successful_count: int = Field(..., description="成功处理数量")
    failed_count: int = Field(..., description="失败处理数量")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class VideoAnalysisResult(BaseModel):
    """视频分析结果模型"""
    video_id: str = Field(..., description="视频ID")
    title: str = Field(..., description="视频标题")
    analysis_result: str = Field(..., description="分析结果")
    success: bool = Field(..., description="是否成功")
    error: Optional[str] = Field(None, description="错误信息")

class FileAnalysisResponse(BaseModel):
    """文件分析响应模型"""
    success: bool = Field(..., description="整体是否成功")
    results: List[VideoAnalysisResult] = Field(..., description="视频分析结果")
    analysis_type: str = Field(..., description="分析类型")
    model_used: str = Field(..., description="使用的AI模型")
    total_videos: int = Field(..., description="总视频数量")
    successful_count: int = Field(..., description="成功处理数量")
    failed_count: int = Field(..., description="失败处理数量")
    total_processing_time: float = Field(..., description="总处理时间(秒)")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="是否成功")
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
