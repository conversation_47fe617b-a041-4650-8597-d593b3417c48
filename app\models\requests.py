"""
API请求数据模型
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from enum import Enum

class AnalysisType(str, Enum):
    """分析类型枚举"""
    SUMMARY = "summary"           # 摘要生成
    EXTRACT = "extract"          # 关键信息提取
    CLASSIFY = "classify"        # 主题分类
    SENTIMENT = "sentiment"      # 情感分析
    KEYWORDS = "keywords"        # 关键词提取

class ModelProvider(str, Enum):
    """AI模型提供商枚举"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"

class TextAnalysisRequest(BaseModel):
    """文本分析请求模型"""
    text: str = Field(..., description="要分析的文本内容", min_length=1, max_length=50000)
    analysis_type: AnalysisType = Field(..., description="分析类型")
    model_provider: Optional[ModelProvider] = Field(None, description="指定AI服务提供商")
    custom_prompt: Optional[str] = Field(None, description="自定义提示词")
    
    @validator('text')
    def validate_text(cls, v):
        if not v.strip():
            raise ValueError('文本内容不能为空')
        return v.strip()

class BatchAnalysisRequest(BaseModel):
    """批量文本分析请求模型"""
    texts: List[str] = Field(..., description="要分析的文本列表", min_items=1, max_items=10)
    analysis_type: AnalysisType = Field(..., description="分析类型")
    model_provider: Optional[ModelProvider] = Field(None, description="指定AI服务提供商")
    custom_prompt: Optional[str] = Field(None, description="自定义提示词")
    
    @validator('texts')
    def validate_texts(cls, v):
        if not v:
            raise ValueError('文本列表不能为空')
        
        for i, text in enumerate(v):
            if not text.strip():
                raise ValueError(f'第{i+1}个文本内容不能为空')
            if len(text) > 50000:
                raise ValueError(f'第{i+1}个文本长度超过限制(50000字符)')
        
        return [text.strip() for text in v]

class FileAnalysisRequest(BaseModel):
    """文件分析请求模型"""
    analysis_type: AnalysisType = Field(..., description="分析类型")
    model_provider: Optional[ModelProvider] = Field(None, description="指定AI服务提供商")
    custom_prompt: Optional[str] = Field(None, description="自定义提示词")
    extract_by_video: bool = Field(False, description="是否按视频分组提取")

class ExtractedTextItem(BaseModel):
    """提取的文本项模型"""
    title: str = Field(..., description="视频标题")
    video_id: str = Field(..., description="视频ID")
    content: str = Field(..., description="文本内容")
    
class CustomAnalysisRequest(BaseModel):
    """自定义分析请求模型"""
    text: str = Field(..., description="要分析的文本内容", min_length=1, max_length=50000)
    prompt: str = Field(..., description="自定义分析提示词", min_length=10, max_length=2000)
    model_provider: Optional[ModelProvider] = Field(None, description="指定AI服务提供商")
    
    @validator('text', 'prompt')
    def validate_not_empty(cls, v):
        if not v.strip():
            raise ValueError('内容不能为空')
        return v.strip()
