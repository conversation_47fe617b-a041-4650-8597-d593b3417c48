"""
AI服务集成模块
支持OpenAI和Anthropic API
"""

import openai
import anthropic
from typing import Dict, Optional, Tuple
import time
import asyncio
from app.config import settings
from app.models.requests import AnalysisType, ModelProvider

class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        
        # 初始化OpenAI客户端
        if settings.OPENAI_API_KEY:
            self.openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        
        # 初始化Anthropic客户端
        if settings.ANTHROPIC_API_KEY:
            self.anthropic_client = anthropic.Anthropic(api_key=settings.ANTHROPIC_API_KEY)
    
    def get_analysis_prompt(self, analysis_type: AnalysisType, custom_prompt: Optional[str] = None) -> str:
        """
        获取分析提示词
        
        Args:
            analysis_type: 分析类型
            custom_prompt: 自定义提示词
            
        Returns:
            完整的提示词
        """
        if custom_prompt:
            return custom_prompt
        
        prompts = {
            AnalysisType.SUMMARY: """
请对以下文本进行摘要，要求：
1. 提取核心观点和关键信息
2. 保持逻辑清晰，语言简洁
3. 长度控制在原文的1/3左右
4. 保留重要的数据和事实

文本内容：
{text}

请生成摘要：
""",
            AnalysisType.EXTRACT: """
请从以下文本中提取关键信息，包括：
1. 主要事件或新闻
2. 重要人物或机构
3. 关键数据和时间
4. 核心观点或结论
5. 技术名词或专业术语

请以结构化的方式呈现提取的信息。

文本内容：
{text}

提取的关键信息：
""",
            AnalysisType.CLASSIFY: """
请对以下文本进行主题分类，并说明分类理由：
1. 确定文本的主要主题类别
2. 识别次要主题
3. 分析内容的领域归属
4. 评估内容的重要程度

文本内容：
{text}

主题分类结果：
""",
            AnalysisType.SENTIMENT: """
请对以下文本进行情感分析：
1. 判断整体情感倾向（积极/消极/中性）
2. 识别情感强度（强烈/中等/轻微）
3. 分析情感表达的具体方面
4. 提取情感关键词

文本内容：
{text}

情感分析结果：
""",
            AnalysisType.KEYWORDS: """
请从以下文本中提取关键词：
1. 识别最重要的10-15个关键词
2. 按重要程度排序
3. 包括人名、地名、机构名
4. 包括专业术语和核心概念
5. 提供每个关键词的简短说明

文本内容：
{text}

关键词提取结果：
"""
        }
        
        return prompts.get(analysis_type, prompts[AnalysisType.SUMMARY])
    
    async def analyze_with_openai(self, text: str, prompt: str) -> Tuple[str, Dict]:
        """
        使用OpenAI进行文本分析
        
        Args:
            text: 要分析的文本
            prompt: 分析提示词
            
        Returns:
            (分析结果, token使用情况)
        """
        if not self.openai_client:
            raise ValueError("OpenAI API密钥未配置")
        
        try:
            start_time = time.time()
            
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "你是一个专业的文本分析助手，能够准确理解和分析各种类型的文本内容。"},
                    {"role": "user", "content": prompt.format(text=text)}
                ],
                temperature=0.3,
                max_tokens=2000
            )
            
            processing_time = time.time() - start_time
            
            result = response.choices[0].message.content
            token_usage = {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens,
                "processing_time": processing_time
            }
            
            return result, token_usage
            
        except Exception as e:
            raise ValueError(f"OpenAI API调用失败: {str(e)}")
    
    async def analyze_with_anthropic(self, text: str, prompt: str) -> Tuple[str, Dict]:
        """
        使用Anthropic进行文本分析
        
        Args:
            text: 要分析的文本
            prompt: 分析提示词
            
        Returns:
            (分析结果, token使用情况)
        """
        if not self.anthropic_client:
            raise ValueError("Anthropic API密钥未配置")
        
        try:
            start_time = time.time()
            
            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model=settings.ANTHROPIC_MODEL,
                max_tokens=2000,
                temperature=0.3,
                system="你是一个专业的文本分析助手，能够准确理解和分析各种类型的文本内容。",
                messages=[
                    {"role": "user", "content": prompt.format(text=text)}
                ]
            )
            
            processing_time = time.time() - start_time
            
            result = response.content[0].text
            token_usage = {
                "input_tokens": response.usage.input_tokens,
                "output_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens,
                "processing_time": processing_time
            }
            
            return result, token_usage
            
        except Exception as e:
            raise ValueError(f"Anthropic API调用失败: {str(e)}")
    
    async def analyze_text(
        self, 
        text: str, 
        analysis_type: AnalysisType,
        model_provider: Optional[ModelProvider] = None,
        custom_prompt: Optional[str] = None
    ) -> Tuple[str, Dict, str]:
        """
        分析文本
        
        Args:
            text: 要分析的文本
            analysis_type: 分析类型
            model_provider: 指定的模型提供商
            custom_prompt: 自定义提示词
            
        Returns:
            (分析结果, token使用情况, 使用的模型)
        """
        # 确定使用的模型提供商
        provider = model_provider or ModelProvider(settings.DEFAULT_MODEL_PROVIDER)
        
        # 获取提示词
        prompt = self.get_analysis_prompt(analysis_type, custom_prompt)
        
        # 根据提供商调用相应的API
        if provider == ModelProvider.OPENAI:
            result, token_usage = await self.analyze_with_openai(text, prompt)
            model_used = settings.OPENAI_MODEL
        elif provider == ModelProvider.ANTHROPIC:
            result, token_usage = await self.analyze_with_anthropic(text, prompt)
            model_used = settings.ANTHROPIC_MODEL
        else:
            raise ValueError(f"不支持的模型提供商: {provider}")
        
        return result, token_usage, model_used
    
    def is_available(self, provider: ModelProvider) -> bool:
        """
        检查指定的AI服务是否可用
        
        Args:
            provider: 模型提供商
            
        Returns:
            是否可用
        """
        if provider == ModelProvider.OPENAI:
            return self.openai_client is not None
        elif provider == ModelProvider.ANTHROPIC:
            return self.anthropic_client is not None
        return False

# 创建全局AI服务实例
ai_service = AIService()
